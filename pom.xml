<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.sungoin</groupId>
	<artifactId>cti</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>jar</packaging>
	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<maven.compiler.source>1.8</maven.compiler.source>
		<maven.compiler.target>1.8</maven.compiler.target>
	</properties>

	<!-- Inherit defaults from Spring Boot -->
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>1.2.5.RELEASE</version>
	</parent>

	<!-- Add typical dependencies for a web application -->
	<dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion> 
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>log4j-over-slf4j</artifactId>
                </exclusion>
            </exclusions> 
        </dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-mail</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-jdbc</artifactId>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dongjin.cti</groupId>
			<artifactId>DJKeygoe</artifactId>
			<version>3.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.mina</groupId>
			<artifactId>mina-core</artifactId>
			<version>2.0.9</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.1</version>
		</dependency>
		<dependency>
			<groupId>org.codehaus.jackson</groupId>
			<artifactId>jackson-mapper-asl</artifactId>
			<version>1.9.4</version>
		</dependency>
		<dependency>
			<groupId>com.thoughtworks.xstream</groupId>
			<artifactId>xstream</artifactId>
			<version>1.4.7</version>
		</dependency>

		<!-- log4j2 -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-slf4j-impl</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<version>4.1.6.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-dbcp2</artifactId>
			<version>2.1.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.4.2</version>
		</dependency>
        <!-- for xcc -->
        <!-- https://jmaven.com/maven/io.nats/jnats --> 
        <dependency>
            <groupId>io.nats</groupId>
            <artifactId>jnats</artifactId>
            <version>2.16.11</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.googlecode.json-simple/json-simple -->
        <dependency>
            <groupId>com.googlecode.json-simple</groupId>
            <artifactId>json-simple</artifactId>
            <version>1.1.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java-util -->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.19.4</version>
        </dependency>
        
        <dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>2.10.4</version>
		</dependency>
		<dependency>
		    <groupId>com.alibaba.nls</groupId>
		    <artifactId>nls-sdk-tts</artifactId>
		    <version>2.2.1</version>
		</dependency>
        <dependency>    
            <groupId>com.alibaba.nls</groupId>  
            <artifactId>nls-sdk-transcriber</artifactId>   
            <version>2.2.1</version>
      </dependency>
		<dependency>
			<groupId>com.aliyun.openservices</groupId>
			<artifactId>ons-client</artifactId>
			<version>1.9.1.Final</version>
		</dependency>
	</dependencies>

	<repositories>
		<repository>
			<id>sungoin-release</id>
			<name>inner nexus server</name>
			<url>http://nexus.sungoin.com/repository/maven-public/</url>
		</repository>
	</repositories>
	<pluginRepositories>
		<pluginRepository>
			<id>sungoin-releases</id>
			<name>inner nexus plugin server</name>
			<url>http://nexus.sungoin.com/repository/maven-public/</url>
		</pluginRepository>
	</pluginRepositories>
	<build>
		<plugins>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>appassembler-maven-plugin</artifactId>
				<version>1.10</version>
				<configuration>
					<repositoryLayout>flat</repositoryLayout>
					<repositoryName>lib</repositoryName>
					<includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>
					<copyConfigurationDirectory>src/main/resources</copyConfigurationDirectory>
					<target>${project.build.directory}</target>
					<daemons>
						<daemon>
							<id>cti-server</id>
							<mainClass>com.sungoin.cti.boot.CTIApplication</mainClass>
							<commandLineArguments>
								<commandLineArgument>start</commandLineArgument>
							</commandLineArguments>
							<platforms>
								<platform>jsw</platform>
							</platforms>
							<generatorConfigurations>
								<generatorConfiguration>
									<generator>jsw</generator>
									<includes>
										<include>linux-x86-64</include>
										<include>windows-x86-64</include>
									</includes>
									<configuration>
										<property>
											<name>configuration.directory.in.classpath.first</name>
											<value>etc</value>
										</property>
										<property>
											<name>set.default.REPO_DIR</name>
											<value>lib</value>
										</property>
										<property>
											<name>wrapper.logfile</name>
											<value>../logs/wrapper.log</value>
										</property>
										<property>
											<name>run.as.user.envvar</name>
											<value>root</value>
										</property>
									</configuration>
								</generatorConfiguration>
							</generatorConfigurations>
							<jvmSettings>
								<initialMemorySize>384M</initialMemorySize>
								<maxMemorySize>768M</maxMemorySize>
								<systemProperties>  
                                    <systemProperty>java.rmi.server.hostname=************</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote.port=8999</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote.authenticate=false</systemProperty>  
                                    <systemProperty>com.sun.management.jmxremote.ssl=false</systemProperty>  
                                </systemProperties> 
								<extraArguments>
									<extraArgument>-server</extraArgument>
								</extraArguments>
							</jvmSettings>
						</daemon>
					</daemons>
				</configuration>
				<executions>
					<execution>
						<id>generate-jsw-scripts</id>
						<phase>package</phase>
						<goals>
							<goal>generate-daemons</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>
</project>