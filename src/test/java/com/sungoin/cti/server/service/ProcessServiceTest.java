/**
 * 
 */
package com.sungoin.cti.server.service;

import static org.junit.Assert.assertFalse;

import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
public class ProcessServiceTest extends BaseTest {

    @Autowired
    private ProcessService processService;

    @Test
    public void testTableExist() throws Exception {
        assertFalse(this.processService.tableExist("111"));
    }

    /**
     * Test method for
     * {@link com.sungoin.cti.server.service.ProcessService#getNextDayTableName()}
     * .
     */
    @Test
    public void testGetNextDayTableName() throws Exception {
        if (this.processService.tableExist(this.processService
            .getNextDayTableName())) {
            this.processService.createTable(this.processService
                .getNextDayTableName());
        }
    }

    /**
     * Test method for
     * {@link com.sungoin.cti.server.service.ProcessService#getTableName()}.
     */
    @Test
    public void testGetTableName() throws Exception {
        System.out.println(this.processService.getTableName());
    }

    /**
     * Test method for
     * {@link com.sungoin.cti.server.service.ProcessService#saveCallData(java.lang.String, java.lang.String, int, int, int, java.lang.String, java.lang.String)}
     * .
     */
    @Test
    public void testSaveCallData() throws Exception {
        this.processService.saveCallData("111", "111", 1, 1, 1, "021333", null);
    }
}
