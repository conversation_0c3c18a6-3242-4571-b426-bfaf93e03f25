package com.sungoin.cti.server.service;

import org.junit.Ignore;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

public class MessageServiceTest extends BaseTest {

    @Autowired
    MessageService message;

    /**
     * Test method for
     * {@link com.sungoin.cti.server.MessageService#sendMail(java.lang.String, java.lang.String, java.lang.String)}
     * .
     */
    @Ignore
    @Test
    public void testSendMail() throws Exception {
        String str = "<EMAIL>,<EMAIL>";
        this.message.sendMail("移动400放号通知", "开始抢号了", str.split(","));
    }
}
