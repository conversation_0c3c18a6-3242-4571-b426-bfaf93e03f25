/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

import com.sungoin.cti.engine.ConferenceMode;
import static com.sungoin.cti.server.scoket.MessageType.BROADCAST;
import static com.sungoin.cti.server.scoket.MessageType.DETECTSPEECH;
import static com.sungoin.cti.server.scoket.MessageType.PLAY;
import com.sungoin.cti.util.JsonHelper;
import com.sungoin.cti.util.LshData;
import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public abstract class AbstractMessageHandler {

    private static final Logger log = LoggerFactory.getLogger(AbstractMessageHandler.class);

    private static boolean showProcessTime;

    public static void setShowProcessTime(boolean showProcessTime) {
        AbstractMessageHandler.showProcessTime = showProcessTime;
    }

    public static boolean isShowProcessTime() {
        return showProcessTime;
    }

    public abstract void process(String msg);

    public abstract int getMaxCapacity();

    public abstract int getCurrentCapacity();

    public abstract void destory();
    @Resource
    private CTIServer ctiServer;

    @SuppressWarnings("unchecked")
    protected void doWork(String msg) {
        Map<String, String> data = null;
        if (isXMLText(msg)) {
            //TODO xml 
            data = new HashMap<String, String>();
        } else {
            data = JsonHelper.json2Object(msg, Map.class);
        }
        MessageType messageType = MessageType.valueOf(data.get("operateType"));
        log.debug("消息类型 {}", messageType.getName());
        String threadName = data.get("threadName");
        int lsh = Integer.parseInt(String.valueOf(data.get("lsh")));
        switch (messageType) {
            case MAKECALL:
                log.debug("make call ...");
                String callData[] = ctiServer.makeCall(data.get("caller"), data.get("callee"), data.get("origCallee"));

                if (callData[0] == null) {
                    CTIRemoteReq req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, Integer.parseInt(callData[2]), null, threadName, lsh);
                    req.send();
                } else {
                    //客户端主动发起外呼，并且没有流水号
                    if (lsh == 0) {
                        lsh = Integer.parseInt(callData[0]);
                        LshData.add(lsh, threadName, LshData.LshType.CALL);
                    }
                    String deviceID = callData[1];
                    CTIRemoteReq req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, CtiState.CTI_SUCCESS, deviceID, threadName, lsh);
                    req.send();
                }

                break;
            case ANSWER:
                int result = ctiServer.answer(data.get("deviceID") + "_" + lsh, threadName, lsh);
                CTIRemoteReq req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case ALERT:
                result = ctiServer.alert(data.get("deviceID") + "_" + lsh, threadName, lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case PLAY:
                boolean loop = Boolean.valueOf(data.get("loop"));
                boolean isQueue = Boolean.valueOf(data.get("isQueue"));
                int maxSecond = Integer.parseInt(String.valueOf(data.get("maxSecond")));
                result = ctiServer.play(data.get("deviceID") + "_" + lsh, data.get("filePath"), loop, isQueue, maxSecond);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case BROADCAST:
                result = ctiServer.broadCast(data.get("deviceID") + "_" + lsh, data.get("file"));
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case PLAYNUMBER:
                result = ctiServer.playNumberList(data.get("deviceID") + "_" + lsh, data.get("numbers"));
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case STOPPLAY:
                result = ctiServer.stopPlay(data.get("deviceID") + "_" + lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case PLAYDTMF:
                result = ctiServer.playDtmf(data.get("deviceID") + "_" + lsh, data.get("dtmf"));
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case RECEIVEDTMF:
                ctiServer.receiveDTMF(data.get("deviceID") + "_" + lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, CtiState.CTI_SUCCESS, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case STOPRECEIVEDTMF:
                ctiServer.stopDTMF(data.get("deviceID") + "_" + lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, CtiState.CTI_SUCCESS, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case RECORD:
                int length = Integer.parseInt(String.valueOf(data.get("length")));
                boolean isAppend = Boolean.valueOf(String.valueOf(data.get("isAppend")));
                // 传入录音时长（单位为：秒），传出录音时长（单位为：毫秒）
                // 此处可能有BUG 单位为100
                length = length * 100;
                result = ctiServer.record(data.get("deviceID") + "_" + lsh, data.get("filePath"), length, isAppend);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case STOPRECORD:
                result = ctiServer.stopRecord(data.get("deviceID") + "_" + lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case CONNECT: {
                int calleeLsh = Integer.parseInt(String.valueOf(data.get("calleeLsh")));
                boolean sameDsp = data.get("sameDsp") == null ? true : Boolean.valueOf(String.valueOf(data.get("sameDsp")));
                result = ctiServer.connectCall(data.get("deviceID") + "_" + lsh, data.get("calleeDeviceID") + "_" + calleeLsh, sameDsp);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.putParamField("calleeDeviceID", data.get("calleeDeviceID")).send();
                break;
            }
            case DISCONNECT: {
                int calleeLsh = Integer.parseInt(String.valueOf(data.get("calleeLsh")));
                result = ctiServer.disConnectCall(data.get("deviceID") + "_" + lsh, data.get("calleeDeviceID") + "_" + calleeLsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.putParamField("calleeDeviceID", data.get("calleeDeviceID")).send();
                break;
            }
            case CREATECONF:
                LshData.add(lsh, threadName, LshData.LshType.CONF);
                ConferenceMode mode = ConferenceMode.valueOf(data.get("mode"));
                String confID = ctiServer.createAndJoinConf(data.get("deviceID") + "_" + lsh, mode);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, CtiState.CTI_SUCCESS, data.get("deviceID"), threadName, lsh);
                req.putParamField("confID", confID).putParamField("mode", mode).send();
                break;
            case JOINCONF:
                LshData.add(lsh, threadName, LshData.LshType.CONF);
                mode = ConferenceMode.valueOf(data.get("mode"));
                confID = data.get("confID");
                ctiServer.joinConf(data.get("deviceID") + "_" + lsh, mode, confID);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, CtiState.CTI_SUCCESS, data.get("deviceID"), threadName, lsh);
                req.putParamField("confID", confID).putParamField("mode", mode).send();
                break;
            case LEAVECONF:
                LshData.add(lsh, threadName, LshData.LshType.CONF);
                result = ctiServer.leaveConf(data.get("deviceID") + "_" + lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case ONHOOK:
                result = ctiServer.onHook(data.get("deviceID") + "_" + lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case DETECTSPEECH:
                result = ctiServer.detectSpeech(data.get("deviceID") + "_" + lsh, data.get("file"), data.get("timeout") == null ? 15000 : Long.parseLong(data.get("timeout")) * 1000);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            case STOPDETECTSPEECH:
                result = ctiServer.stopDetectSpeech(data.get("deviceID") + "_" + lsh);
                req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, messageType, result, data.get("deviceID"), threadName, lsh);
                req.send();
                break;
            default:
                break;

        }
    }

    private boolean isXMLText(String msg) {
        return msg.startsWith(CTIRemoteReq.MSG_TYPE_XML);
    }
}
