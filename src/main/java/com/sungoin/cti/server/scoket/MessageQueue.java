/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.PostConstruct;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service(value = "messageQueue")
public class MessageQueue extends AbstractMessageHandler implements Runnable {

	private final static org.slf4j.Logger log = LoggerFactory.getLogger(MessageQueue.class);
	private final BlockingQueue<String> queue = new ArrayBlockingQueue<String>(Configuration.getMessageMaxQueueCount());
	private static final ExecutorService es = Executors.newCachedThreadPool();
	private volatile boolean isRun = true;

	private MessageQueue() {
	}

	@PostConstruct
	public void init() {
		new Thread(this).start();
	}

	@Override
	public void run() {
		log.debug(" messageQueue run ...");
		while (isRun) {
			try {
				final String msg = queue.take();
				es.submit(new Runnable() {
					@Override
					public void run() {
						try {
							doWork(msg);
						} catch (Exception ex) {
							CTIRemoteReq req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, null, -1, null, null, -1);
							req.putParamField("errorMsg", msg)
								.putParamField("reason", ex.getMessage()).send();
						}
					}
				});
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
		}
	}

	@Override
	public void process(String msg) {
		boolean result = queue.offer(msg);
		if (!result) {
			log.error(" MessageQueue is full !");
		}

	}

	@Override
	public int getMaxCapacity() {
		return Configuration.getMessageMaxQueueCount();
	}

	@Override
	public int getCurrentCapacity() {
		return queue.size();
	}

	@Override
	public void destory() {
		log.debug(" messageQueue destory  success ....");
		isRun = false;
	}

}
