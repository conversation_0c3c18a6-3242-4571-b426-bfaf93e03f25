/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

import com.sungoin.cti.engine.exception.InitEngineFailException;
import java.io.IOException;
import java.util.Properties;

/**
 *
 * <AUTHOR> 2015-5-4
 */
public final class Configuration {

    private static final Properties prop = new Properties();
    public static final String DEFAULT_MESAGEHANDLER = "defaultMesageHandler";

    public static final String VOCPATH = "VocPath";
    public static final String MESSAGE_MAX_THREAD_COUNT = "message.maxThreadCount";
    public static final String MESSAGE_MAX_QUEUE_COUNT = "message.maxQueueCount";

    public static final String MAX_THREAD_COUNT = "maxThreadCount";

	private Configuration() {
	}


    static {
        try {
            prop.load(Configuration.class.getClassLoader().getResourceAsStream("server.properties"));
        } catch (IOException ex) {
            throw new InitEngineFailException("can not load xms.properties!", ex);
        }
    }

    public static String getProperty(String key) {
        return prop.getProperty(key);
    }

    public static String getProperty(String key, String defaultValue) {
        return prop.getProperty(key, defaultValue);
    }

    public static int getMaxThreadCount() {
        int count = Integer.parseInt(getProperty(MAX_THREAD_COUNT));
        return count;
    }

    public static int getMessageMaxThreadCount() {
        int count = Integer.parseInt(getProperty(MESSAGE_MAX_THREAD_COUNT));
        return count;
    }

    public static int getMessageMaxQueueCount() {
        int count = Integer.parseInt(getProperty(MESSAGE_MAX_QUEUE_COUNT));
        return count;
    }
}
