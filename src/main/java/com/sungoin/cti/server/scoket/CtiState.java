/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

/**
 *
 * <AUTHOR>
 */
public final class CtiState {

	public static final int CTI_SUCCESS = 1; //調用成功
	public static final int CTI_VOICE_FULL = -2; //語音通道已滿
	public static final int CTI_DEVICE_FULL = -4; //設備非空閒 
	public static final int CTI_FILEPATH_ERROR = -3; //文件路徑不存在
	public static final int CONFERENCE_FULL = -5;     //会议设备已满
	public static final int PLAY_VOICE_FULL = -6;    //放音失败
	public static final int UNKNOWN_FULL = -7;    //未知原因失败

	private CtiState() {
	}
	
}
