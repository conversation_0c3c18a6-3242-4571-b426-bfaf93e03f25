package com.sungoin.cti.server.scoket;

import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import java.util.List;
import org.apache.mina.core.service.IoAcceptor;

import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.filter.logging.LoggingFilter;
import org.apache.mina.transport.socket.nio.NioSocketAcceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MinaTimeServer {

	public static final String HEART_BEAT = Configuration.getProperty("mina.heart.beat");

	private static final Logger log = LoggerFactory.getLogger(MinaTimeServer.class);
	private static final int PORT = Integer.parseInt(Configuration.getProperty("mina.port"));

	private static final MinaTimeServer minaserver = new MinaTimeServer();

	private IoAcceptor acceptor;
	public volatile boolean running;

	public static MinaTimeServer getInstance() {
		return minaserver;
	}

	private MinaTimeServer() {
	}

	public synchronized boolean initServer() {
		try {
			// 创建服务器监听,即创建一个非阻塞的Server端Socket,用NIO
			acceptor = new NioSocketAcceptor();

			// 增加日志过滤器
			acceptor.getFilterChain().addLast("logger", new LoggingFilter());

			/*
			 * 创建接收数据的过滤器,设定这个过滤器将一行一行(/r/n)的读取数据
			 * 这个适合用来传输字符串,去掉这行适合与C-SOCKET通信
			 * */
			acceptor.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
			/*
			 * ObjectSerializationCodecFactory用来传输对象,不适合传输字符串
			 * */
			//acceptor.getFilterChain().addLast("myChin", new ProtocolCodecFilter(new ObjectSerializationCodecFactory()));
			// 指定业务逻辑处理器
			acceptor.setHandler(new ServerHandler());

			// 设置buffer的长度
			acceptor.getSessionConfig().setReadBufferSize(2048);
			int timeOut = Integer.parseInt(Configuration.getProperty("mina.session.timeOut"));
			// 设置连接超时时间
			acceptor.getSessionConfig().setIdleTime(IdleStatus.BOTH_IDLE, timeOut);
			((NioSocketAcceptor) acceptor).setReuseAddress(true);
			log.info("启动mina接收客户端消息，mina端口为：{}", PORT);
			// 绑定端口
			acceptor.bind(new InetSocketAddress(PORT));
			running = true;
		} catch (Exception e) {
			acceptor.unbind();
			acceptor.dispose(true);
			running = false;
			log.error("在MinaTimeServer中的initServer方法中出错原因是:" + e.getMessage());
			throw new IllegalStateException("MinaTimeServer启动失败");
		}
		return running;
	}

	public synchronized void stopServer() {
		log.info("start stop socket server...");
		this.acceptor.unbind();
		this.acceptor.dispose(true);
		this.running = false;
		log.info("stop socket server ok.");
	}

	public IoAcceptor getAcceptor() {
		return acceptor;
	}

	public void broadcastMessageWhenConnected(final List<String> messages) {
		new Thread(new Runnable() {
			@Override
			public void run() {
				try {
					int count = 0;
					while (acceptor.getManagedSessionCount() == 0 && count < 20) {
						log.info("client not connected yet!");
						count++;
						Thread.sleep(1000);
					}
//					log.info("send messages size={}, details={}", messages.size(), messages);
					for (String msg : messages) {
						broadcastMessage(msg);
					}
				} catch (Exception ex) {
					log.error(ex.getMessage(),ex);
				}
			}
		}).start();
	}
    
    public void broadcastMessage(final List<String> messages) {
		for (String msg : messages) {
            broadcastMessage(msg);
        }
	}

	public void broadcastMessage(String message) {
		for (IoSession session : this.acceptor.getManagedSessions().values()) {
			if(session.isConnected()) {
                session.write(message);
            }
		}
	}

	public void sendMessage(long sessionId, String message) {
		IoSession session = this.acceptor.getManagedSessions().get(sessionId);
		if (session == null) {
			log.warn("not find session by Id " + sessionId);
		} else {
			session.write(message);
		}
	}
}
