/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

import com.sungoin.cti.util.JsonHelper;
import com.sungoin.cti.util.XMLHelper;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class CTIRemoteReq {

    public static final String MSG_TYPE_JSON = "josn";
    public static final String MSG_TYPE_XML = "xml";
    private final String msgType;
    private final  Map<String, Object> data;

    private CTIRemoteReq(String msgType, int ctiState, String deviceID, String threadName, int lsh) {
        data = new HashMap<String, Object>();
        this.msgType = msgType;
        data.put("eventState", ctiState);
        data.put("deviceID", deviceID);
        data.put("threadName", threadName);
        data.put("lsh", lsh);
    }

    public static CTIRemoteReq getMessage(String msgType, MessageType type, int ctiState, String deviceID, String threadName, int lsh) {
        CTIRemoteReq req = new CTIRemoteReq(msgType, ctiState, deviceID, threadName, lsh);
        req.data.put("messageType", type);
        return req;
    }

    public static CTIRemoteReq getEvent(String msgType, EventType type, int ctiState, String deviceID, String threadName, int lsh) {
        CTIRemoteReq req = new CTIRemoteReq(msgType, ctiState, deviceID, threadName, lsh);
        req.data.put("eventType", type);
        return req;
    }

    public CTIRemoteReq putParamField(String name, Object value) {
        data.put(name, value);
        return this;
    }
	
	public String getSendMessage() {
		StringBuilder sb = new StringBuilder();
        if (MSG_TYPE_JSON.equals(this.msgType)) {
            sb.append(JsonHelper.object2Json(data));
        } else {
            Map<String, Class> classMap = new HashMap<String, Class>();
//            classMap.put("name", String.class);
//            classMap.put("age", java.util.Map.Entry.class);
//            classMap.put("xml", java.util.Map.class);
            sb.append("<xml>");
            sb.append(XMLHelper.bean2xml(classMap, data));
            sb.append("</xml>");
        }
        sb.append("\r");
		return sb.toString();
	}

    public void send() {
        StringBuilder sb = new StringBuilder();
        if (MSG_TYPE_JSON.equals(this.msgType)) {
            sb.append(JsonHelper.object2Json(data));
        } else {
            Map<String, Class> classMap = new HashMap<String, Class>();
//            classMap.put("name", String.class);
//            classMap.put("age", java.util.Map.Entry.class);
//            classMap.put("xml", java.util.Map.class);
            sb.append("<xml>");
            sb.append(XMLHelper.bean2xml(classMap, data));
            sb.append("</xml>");
        }
        sb.append("\r");
		MinaTimeServer.getInstance().broadcastMessage(sb.toString());
    }
}
