/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

/**
 *
 * <AUTHOR>
 */
public enum MessageType {

    MAKECALL("呼叫"), 
    ANSWER("应答"), 
    ALERT("振鈴"), 
    PLAY("放音"), 
    PLAYNUMBER("报工号"),
    STOPPLAY("停止放音"),
    PLAYDTMF("放码"),
    RECEIVEDTMF("收码"),
    STOPRECEIVEDTMF("停止收码"),
    RECORD("录音"),
    CONNECT("连接"),
    DISCONNECT("断开连接"),
    STOPRECORD("停止录音"),
    CREATECONF("创建会议"),
    JOINCONF("加入会议"),
	LEAVECONF("离开会议"),
    ONHOOK("挂机"),
    BROADCAST("广播"),
    DETECTSPEECH("语音识别"),
    STOPDETECTSPEECH("停止语音识别");
    
    
    private String name;

    private MessageType(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
