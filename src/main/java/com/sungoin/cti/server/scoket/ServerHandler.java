package com.sungoin.cti.server.scoket;

import com.sungoin.cti.util.SpringHelper;
import java.net.InetSocketAddress;
import org.apache.mina.core.service.IoHandler;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ServerHandler extends IoHandlerAdapter implements IoHandler {

    private static final Logger log = LoggerFactory.getLogger(ServerHandler.class);
    private final AbstractMessageHandler messageHandler = (AbstractMessageHandler) SpringHelper.getSpringBean(Configuration.getProperty("defaultMesageHandler"));
    private static final String filterIp = Configuration.getProperty("mina.IpAddr");

    @Override
    public void sessionCreated(IoSession session) {
        String ip = ((InetSocketAddress) session.getRemoteAddress()).getAddress().getHostAddress();
        log.info("session:{} 客户端新连接地址：{}",session.getId(), ip);
        boolean isAuthorize = false;
        if ("*".equals(filterIp)) {
            isAuthorize = true;
        } else {
            String[] ipArray = filterIp.split("&");
            for (String str : ipArray) {
                if (ip.equals(str)) {
                    isAuthorize = true;
                    break;
                }
            }
        }
        if (!isAuthorize) {
            log.warn("客户端ip {} 不合法，拒绝连接。。。", ip);
            session.close(true);
        }

    }

    @Override
    public void exceptionCaught(IoSession session, Throwable cause)
        throws Exception {
        log.error("session:{} 异常exceptionCaught：{}", session.getId(), cause.getMessage(), cause);
        session.close(true);
    }

    @Override
    public void messageReceived(final IoSession session, Object message) throws Exception {
        final String msg = message.toString();
        log.debug("session:{} recieved message : {}",session.getId(), msg);
        if (msg.equals(MinaTimeServer.HEART_BEAT)) {
            return;
        }
        if (AbstractMessageHandler.isShowProcessTime()) {
            long startTime = System.currentTimeMillis();
            messageHandler.process(msg);
            long ss = (System.currentTimeMillis() - startTime);
            log.debug("处理任务耗时{}毫秒", ss);
        } else {
            messageHandler.process(msg);
        }

    }

    @Override
    public void sessionIdle(IoSession session, IdleStatus status)
        throws Exception {
        log.debug("session:{} 连接超时。。。{}",session.getId(), session.getRemoteAddress());
		session.close(true);
    }

    @Override
    public void sessionClosed(IoSession session) throws Exception {
        log.info("session:{} 客户端断开地址：{}", session.getId(), session.getRemoteAddress());
    }

    @Override
    public void sessionOpened(IoSession session) throws Exception {
        log.info("session:{} 连接被打开", session.getId());
    }
}
