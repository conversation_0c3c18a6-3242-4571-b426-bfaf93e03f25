package com.sungoin.cti.server.scoket;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service(value = "messageHandlerAsync")
public class MessageHandlerAsync extends AbstractMessageHandler {

    private static final ExecutorService es = Executors.newFixedThreadPool(Configuration.getMessageMaxThreadCount());
    private static final Logger log = LoggerFactory.getLogger(MessageHandlerAsync.class);

    @Override
    public void process(final String msg) {
        es.submit(new Runnable() {
            @Override
            public void run() {
                try {
                    doWork(msg);
                } catch (Exception ex) {
                    CTIRemoteReq req = CTIRemoteReq.getMessage(CTIRemoteReq.MSG_TYPE_JSON, null, -1, null, null, -1);
                    req.put<PERSON>aram<PERSON>ield("errorMsg", msg).putParamField("reason", ex.getMessage()).send();
                    log.error(ex.getMessage(), ex);
                }

            }
        });
    }

    @Override
    public int getMaxCapacity() {
        return Configuration.getMessageMaxThreadCount();
    }

    @Override
    public int getCurrentCapacity() {

        return ((ThreadPoolExecutor) es).getActiveCount();
    }

    @Override
    public void destory() {
        es.shutdownNow();
        log.debug(" messageHandlerAsync destory  success ....");
    }

}
