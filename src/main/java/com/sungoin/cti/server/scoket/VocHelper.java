/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR> 2015-6-8
 */
public final class VocHelper {

	private static final Map<Character, String> numberVoc = new HashMap<Character, String>();
	//初始化放音文件

	private VocHelper() {
	}

	static {
		String path = Configuration.getProperty(Configuration.VOCPATH);
		numberVoc.put('0', path + "/default/0.wav");
		numberVoc.put('1', path + "/default/1.wav");
		numberVoc.put('2', path + "/default/2.wav");
		numberVoc.put('3', path + "/default/3.wav");
		numberVoc.put('4', path + "/default/4.wav");
		numberVoc.put('5', path + "/default/5.wav");
		numberVoc.put('6', path + "/default/6.wav");
		numberVoc.put('7', path + "/default/7.wav");
		numberVoc.put('8', path + "/default/8.wav");
		numberVoc.put('9', path + "/default/9.wav");
	}
	
	public static final String getCharVoc(char c) {
		return numberVoc.get(c);
	}
}
