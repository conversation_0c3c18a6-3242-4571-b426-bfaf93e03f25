/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.scoket;

import com.sungoin.cti.engine.ConferenceMode;
import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface CTIServer {

    public void start();

    public void stop();

    public Date getStartTime();

    public int answer(String callDeviceId, String threadName, int lsh);

    public int alert(String callDeviceId, String threadName, int lsh);

    public int play(String callDeviceId, String fileName, boolean loop, boolean isQueue,int maxSecond);
    
    public int broadCast(String callDeviceId, String fileName);

    public int stopPlay(String callDeviceId);

    public int record(String callDeviceId, String fileName, int length, boolean isAppend);

    public int stopRecord(String callDeviceId);

    public int playDtmf(String callDeviceId, String dtmfStr);

    public void receiveDTMF(String callDeviceId);

    public void stopDTMF(String callDeviceId);

    public int connectCall(String callerDeviceID, String calleeDeviceID);
    
    public int connectCall(String callerDeviceID, String calleeDeviceID, boolean sameDsp);

    public int disConnectCall(String callerDeviceID, String calleeDeviceID);

    public String[] makeCall(String caller, String callee);
	
	public String[] makeCall(String caller, String callee, String origCallee);

    public int onHook(String callDeviceId);

    public int getDeviceState(String deviceId);

    public DeviceStatistic getDeviceStatistic(DeviceType dt);

    public List<DeviceStatistic> getAllDeviceStatistic();

    public int resetDevice(String deviceId);

    public int playNumberList(String deviceId, String numbers);

    public String joinConf(String callDeviceId, ConferenceMode mode, String confDeviceID);

    public String createAndJoinConf(String callDeviceId, ConferenceMode mode);

    public int leaveConf(String callDeviceId);
	
	public int getLicenseDays();
	
	public String getTrunkLineState();
	
	public void saveOrigTalkNote(String caller, String callee, int mode,
        int channel, int callType, String originalNo, String customerNo);
	
	public void addConnectCall(String callId, String threadName, int callType);
	
	public void removeConnectCall(String callId);
    
    public void cleanInvalidKeyUp();
    
    public int detectSpeech(String callDeviceId, String file, long timeout);
    
    public int stopDetectSpeech(String callDeviceId);
}
