package com.sungoin.cti.server.config;

import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(locations = "classpath:conf/common.properties")
public class CommonSetting {

    private String platformId;
    private String personalMail;
    private int usageRate;
	private String engineImpl;
    private String prefix;
    private String allwayCaller;
    private String platformName;
    private String calleePrefix;
    private String callMaxSeconds;

    public String getPlatformId() {
        return this.platformId;
    }

    public void setPlatformId(String platformId) {
        this.platformId = platformId;
    }

    public String getPersonalMail() {
        return this.personalMail;
    }

    public void setPersonalMail(String personalMail) {
        this.personalMail = personalMail;
    }

    public int getUsageRate() {
        return this.usageRate;
    }

    public void setUsageRate(int usageRate) {
        this.usageRate = usageRate;
    }

	public String getEngineImpl() {
		return engineImpl;
	}

	public void setEngineImpl(String engineImpl) {
		this.engineImpl = engineImpl;
	}

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public String getAllwayCaller() {
        return allwayCaller;
    }

    public void setAllwayCaller(String allwayCaller) {
        this.allwayCaller = allwayCaller;
    }

    public String getPlatformName() {
        return platformName;
    }

    public void setPlatformName(String platformName) {
        this.platformName = platformName;
    }

    public String getCalleePrefix() {
        return calleePrefix;
    }

    public void setCalleePrefix(String calleePrefix) {
        this.calleePrefix = calleePrefix;
    }

    public String getCallMaxSeconds() {
        return callMaxSeconds;
    }

    public void setCallMaxSeconds(String callMaxSeconds) {
        this.callMaxSeconds = callMaxSeconds;
    }
}
