package com.sungoin.cti.server.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sungoin.cti.server.service.ProcessService;
import javax.annotation.Resource;

@Component
@EnableScheduling
@PropertySource("classpath:conf/common.properties")
public class CheckTrunkUsageRateTask {

    private static final Logger LOGGER = LoggerFactory
        .getLogger(CheckTrunkUsageRateTask.class);

    @Resource
    private ProcessService processService;
    
    @Scheduled(cron = "${jobs.schedule}")
    public void process() {
        try {
            processService.checkTrunkUsageRate();
        } catch (Exception e) {
            LOGGER.error("检查通道使用率异常:", e);
        }
    }
}
