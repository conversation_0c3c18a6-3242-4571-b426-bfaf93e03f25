/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.server.task;

import com.sungoin.cti.server.config.CommonSetting;
import com.sungoin.cti.server.service.MessageService;
import com.sungoin.cti.server.service.ProcessService;
import com.sungoin.cti.util.ConcurrentHelper;
import java.io.File;
import java.io.RandomAccessFile;
import java.util.Date;
import java.util.regex.Pattern;
import javax.annotation.Resource;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */
//@Component
//@EnableScheduling
@PropertySource("classpath:conf/common.properties")
public class RscCheckTask {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckCallMaxTimeTask.class);
    private static final String FILE_PATH = "/usr/keygoe/log/";
    private static final String FILE_NAME_PRE = "ISUP_trace_1_";
    private static final Pattern PATTERN = Pattern.compile("buf.{30}00 12 $");
    private static final Pattern PATTERN2 = Pattern.compile("buf.{27}[13579BDF]0 0[0-3] 12 $");
    private String lastCheckFile = null;
    private long lastCheckPosition = 0;
    private String content;
    
    @Resource
    private ProcessService processService;
    
    @Resource
    private MessageService messageervice;
    
    @Resource
	private CommonSetting commonSetting;

//    @Scheduled(fixedDelayString = "${rsc_check_delay}", initialDelayString = "${rsc_check_delay}")
    public void process() {
        try {
            LOGGER.debug("RSC检查任务开始执行。。。");
            if (this.rscCheck()) {
                LOGGER.warn("检查到RSC消息，挂断当前通话！");
//                processService.resetConnectTrunk();
                String title = "平台：" + this.commonSetting.getPlatformId() + "检测到RSC消息";
                String message = title + "：" + content;
                String[] receiver = this.commonSetting.getPersonalMail().split(",");
                ConcurrentHelper.doInBackground(() -> {
                    messageervice.sendMail(title, message, receiver);
                });
            }
            LOGGER.debug("RSC检查任务执行结束。。。");
        } catch (Exception e) {
            LOGGER.error("RSC检查异常:", e);
        }
    }

    private boolean rscCheck() {
        boolean find = false;
        String currentFile = getCurrentFileName();
        File file = new File(FILE_PATH + currentFile);
        if (this.lastCheckFile != null && !this.lastCheckFile.equals(currentFile)) {
            find = this.rscCheck(new File(FILE_PATH + this.lastCheckFile), this.lastCheckPosition);
            this.lastCheckFile = null;
            if(find) {
                lastCheckPosition = file.exists() ? file.length() : 0;
            } else {
                this.lastCheckPosition = 0;
            }
        }
        if(!find) {
            find = this.rscCheck(file, this.lastCheckPosition);
        }
        return find;
    }

    private String getCurrentFileName() {
        Date now = new Date();
        return FILE_NAME_PRE + DateFormatUtils.format(now, "yyyyMMdd_HH") + ".txt";
    }

    public boolean rscCheck(File file, long position){
        LOGGER.debug("开始检查RSC，file = {}, pos = {}", file.getName(), position);
        if (!file.exists() || file.length() <= position) {
            return false;
        }                               
        boolean find = false;
        try(RandomAccessFile raf = new RandomAccessFile(file, "r")) {
            raf.seek(lastCheckPosition);
            String line;
            while ((line = raf.readLine()) != null) {
                if (PATTERN.matcher(line).find() && !PATTERN2.matcher(line).find()) {
                    find = true;
                    this.content = line;
                    LOGGER.info("查找到rsc消息：{}", line);
                    break;
                }
            }
            lastCheckFile = file.getName();
            lastCheckPosition = raf.length();
            return find;
        } catch (Exception e) {
            LOGGER.error(e.getMessage(), e);
            return false;
        }
    }
}
