package com.sungoin.cti.server.task;

import javax.annotation.Resource;

import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sungoin.cti.server.service.ProcessService;

@Component
@EnableScheduling
public class CommonTask {

    @Resource
    private ProcessService processService;

    @Scheduled(cron = "0 0 2 * * ?")
    public void createNextDayTable() {
        String nextDayTableName = this.processService.getNextDayTableName();
        if (!this.processService.tableExist(nextDayTableName)) {
            this.processService.createTable(nextDayTableName);
        }
    }
    
    @Scheduled(cron = "0 0 4 * * ?")
    public void cleanInvalidKeyUp() {
        this.processService.cleanInvalidKeyUp();
    }
}
