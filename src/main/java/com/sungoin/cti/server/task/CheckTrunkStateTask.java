package com.sungoin.cti.server.task;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.sungoin.cti.server.service.ProcessService;
import com.sungoin.cti.util.SpringHelper;

//@Component
//@EnableScheduling
//@PropertySource("classpath:conf/common.properties")
public class CheckTrunkStateTask {

    private static final Logger LOGGER = LoggerFactory
        .getLogger(CheckTrunkStateTask.class);

    @Scheduled(cron = "${jobs.schedule}")
    public void process() {
        try {
            ProcessService processService = SpringHelper
                .getSpringBean(ProcessService.class);
            processService.checkTrunkState();
        } catch (Exception e) {
            LOGGER.error("检查通道状态异常:", e);
        }
    }
}
