/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.server.task;

import com.sungoin.cti.server.service.ProcessService;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.PropertySource;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 */
@Component
@EnableScheduling
@PropertySource("classpath:conf/common.properties")
public class CheckCallMaxTimeTask {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(CheckCallMaxTimeTask.class);
    
    @Resource
    private ProcessService processService;
    
    @Scheduled(fixedDelayString = "${check_call_delay}", initialDelayString = "${check_call_delay}")
    public void process() {
        try {
            LOGGER.debug("检查超长通话任务开始执行。。。");
            processService.checkCallTime();
            LOGGER.debug("检查超长通话任务执行结束。。。");
        } catch (Exception e) {
            LOGGER.error("检查超长通话异常:", e);
        }
    }
}
