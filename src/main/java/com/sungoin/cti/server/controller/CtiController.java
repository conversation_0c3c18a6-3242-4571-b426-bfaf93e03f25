/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.server.controller;

import com.sungoin.cti.server.service.ProcessService;
import java.text.ParseException;
import java.util.Date;
import javax.annotation.Resource;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/cti")
public class CtiController {
    private static final Logger log = LoggerFactory.getLogger(CtiController.class);
    
    @Resource
    private ProcessService service;
    
    @RequestMapping(value = "/resetConnectTrunk")
    public String resetConnectTrunk(String resetTime) throws ParseException {
        log.info("执行重置通道请求，time={}", resetTime);
        Date date = DateUtils.parseDate(resetTime, "yyyy-MM-dd HH:mm:ss");
        service.resetConnectTrunk(date);
        return "ok";
    }
}
