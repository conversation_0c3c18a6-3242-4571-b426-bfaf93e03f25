/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class KeyUp {

private static final Logger log = LoggerFactory.getLogger(KeyUp.class);
    private static final Map<String, KeyUp> DTMF_MAP = new ConcurrentHashMap<String, KeyUp>();

    private final long createTime;
    private final String endKey;
    private final StringBuffer dtmf;

    public KeyUp(String endKey) {
        this.endKey = endKey;
        this.createTime = System.currentTimeMillis();
        if(this.endKey == null) {
            dtmf = null;
        } else {
            dtmf = new StringBuffer();
        }
    }

    private String append(String key) {
        if (endKey == null) {
            return key;
        }
        if (!key.equals(endKey)) {
            dtmf.append(key);
            return null;
        }
        String code = dtmf.append(key).toString();
        dtmf.delete(0, dtmf.length());
        return code;
    }

    public static void initDtfm(String deviceID, String endKey) {
        KeyUp keyUp = new KeyUp(endKey);
        DTMF_MAP.put(deviceID, keyUp);
    }

    public static String appendDTMF(String deviceID, String key) {
        KeyUp keyUp = DTMF_MAP.get(deviceID);
        if (keyUp == null) {
            return null;
        }
        return keyUp.append(key);
    }

    public static void cleanDtmf(String deviceID) {
        DTMF_MAP.remove(deviceID);
    }
    
    public static void cleanInvalidKeyUp() {
        long now = System.currentTimeMillis();
        log.debug("before clean dtfmMap size is {}", DTMF_MAP.size());
        DTMF_MAP.keySet().forEach(id -> {
            if(now - DTMF_MAP.get(id).createTime > 1000 * 60 * 60 * 2) {
                DTMF_MAP.remove(id);
            }
        });
        log.debug("after clean dtfmMap size is {}", DTMF_MAP.size());
    }
}
