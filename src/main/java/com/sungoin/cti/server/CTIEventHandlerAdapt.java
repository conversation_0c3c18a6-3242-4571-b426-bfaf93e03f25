/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server;

import com.sungoin.cti.util.ConcurrentHelper;
import com.sungoin.cti.server.scoket.CTIServer;
import com.sungoin.cti.engine.CTIEventHandler;
import com.sungoin.cti.server.scoket.EventType;
import com.sungoin.cti.engine.CTIEvent;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.server.scoket.CTIRemoteReq;
import com.sungoin.cti.util.LshData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class CTIEventHandlerAdapt implements CTIEventHandler {

	private static final Logger log = LoggerFactory.getLogger(CTIEventHandlerAdapt.class);

	private final CTIServer server;

	public CTIEventHandlerAdapt(CTIServer server) {
		this.server = server;
	}

	@Override
	public void callIncome(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("callIncome... caller is {},callee is {} deviceId is {}", data.getCaller(), data.getCallee(), data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.CALLINCOME, data.getState(),
			data.getDeviceID(), null, data.getLsh());
		req.putParamField("caller", data.getCaller())
			.putParamField("callee", data.getCallee())
			.send();
		server.saveOrigTalkNote(data.getCaller(), data.getCallee(), Integer.valueOf(data.getModule()), 
			Integer.valueOf(data.getChannel()), 0, data.getCallee(), null);
	}

	@Override
	public void offHook(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("offHook...deviceID is {}", data.getDeviceID());
		String threadName = LshData.getThreadName(data.getLsh(), LshData.LshType.CALL);
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.OFFHOOK, data.getState(),
			data.getDeviceID(), threadName, data.getLsh());
		req.putParamField("caller", data.getCaller())
			.putParamField("callee", data.getCallee())
			.send();
//        server.record(event.getEventData().getDeviceID(), "3.wav", 0, false);
		server.addConnectCall(data.getDeviceID() + "_" + data.getLsh(), threadName, 1);
	}

	@Override
	public void onHook(CTIEvent event) {
		final EventData data = event.getEventData();
		log.debug("onHook...deviceID is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.ONHOOK, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
		req.putParamField("deviceID", data.getDeviceID())
			.putParamField("eventState", event.getEventData().getState())
			.send();
		final String callId = data.getDeviceID() + "_" + data.getLsh();
		KeyUp.cleanDtmf(callId);

//        if (data.isCallOver()) {
		ConcurrentHelper.doInBackground(new Runnable() {
			@Override
			public void run() {
				try {
					server.removeConnectCall(callId);
					Thread.sleep(1000);
				} catch (Exception ex) {
					log.error(ex.getMessage(),ex);
				}
				LshData.remove(data.getLsh(), LshData.LshType.CALL);
			}
		});

//        }
	}

	@Override
	public void playEnd(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("playEnd...deviceID is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.PLAYEND, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
		req.send();

	}

	@Override
	public void receiveDTMF(CTIEvent event) {
		EventData data = event.getEventData();
		log.info("dtmf is {}", data.getDtmf());
		String code = KeyUp.appendDTMF(data.getDeviceID() + "_" + data.getLsh(), data.getDtmf());
		if (code != null) {
			log.debug("code is ..{}", code);
//             WaitUtil.notifyWaiter(WaitCondition.TRUNK, data.getDeviceID(), true);
			CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.RECEIVEDTMF, data.getState(),
				data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
			req.putParamField("dtmf", data.getDtmf()).send();
		}

	}

	@Override
	public void recordEnd(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("recordEnd...deviceID is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.RECORDEND, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
		req.send();
	}

	@Override
	public void callOut(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("callOut...deviceID is {}", data.getDeviceID());
		String threadName = LshData.getThreadName(data.getLsh(), LshData.LshType.CALL);
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.CALLOUT, data.getState(),
			data.getDeviceID(), threadName, data.getLsh());
        //外呼增加失败码
        req.putParamField("errorCode", data.getErrorCode());
		req.send();
		
		if(data.getState() == 1 && data.getLsh() > 0) {
			//外呼成功，记录数据库
			server.addConnectCall(data.getDeviceID() + "_" + data.getLsh(), threadName, 0);
		}
	}

	@Override
	public void joinToConf(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("joinToConf...deviceID is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.JOINCONF, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
		req.putParamField("confID", data.getUsedDeviceID()).putParamField("mode", data.getMode()).send();
	}

	@Override
	public void leaveConf(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("leaveConf...deviceID is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.LEAVECONF, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CONF), data.getLsh());
		req.putParamField("confID", data.getUsedDeviceID()).putParamField("mode", data.getMode()).send();
		LshData.remove(data.getLsh(), LshData.LshType.CONF);

	}

	@Override
	public void alert(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("alert...deviceID is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.ALERT, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
		req.send();

	}

	@Override
	public void clearConf(CTIEvent event) {
		EventData data = event.getEventData();
		log.debug("clearConf...deviceID is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.CLEARCONF, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CONF), data.getLsh());
		req.putParamField("confID", data.getUsedDeviceID()).putParamField("mode", data.getMode()).send();
	}

    @Override
    public void cdr(CTIEvent event) {
        EventData data = event.getEventData();
		log.debug("cdr... deviceId is {}", data.getDeviceID());
		CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.CDR, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
		req.putParamField("record", data.getRecord()).send();
    }

    @Override
    public void detectSpeech(CTIEvent event) {
        EventData data = event.getEventData();
        log.debug("detectSpeech... deviceId is {},text = {}", data.getDeviceID(), data.getText());
        CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.DETECTSPEECH, data.getState(),
			data.getDeviceID(), LshData.getThreadName(data.getLsh(), LshData.LshType.CALL), data.getLsh());
        req.putParamField("text", data.getText()).send();
    }

}
