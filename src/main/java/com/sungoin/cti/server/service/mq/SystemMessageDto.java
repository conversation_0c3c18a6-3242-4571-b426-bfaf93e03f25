/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.server.service.mq;

import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class SystemMessageDto {
    /**
     * 400号码
     */
    private String numberCode;
    /**
     * 合作商主键
     */
    private String agentId;
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 消息编码，如果为空，则直接获取messageContent、messageTitle
     */
    private String messageCode;

    /**
     * 消息内容中包含的参数
     */
    private Map<String, String> messageParam;

    /**
     * 消息内容，如果改字段不为空，则忽略模板中配置的消息、忽略messageParam参数
     */
    private String messageContent;
    /**
     * 消息标题
     */
    private String messageTitle;
    /**
     * 点击消息，跳转的请求路径
     */
    private String messageUrl;

    /**
     * 提醒类型
     */
    private String messageType;

    /**
     * 合作商名称
     */
    private String agentName;

    /**
     * 客服/合作商负责人
     */
    private String customerStaffNameOrAgentOwner;

    /**
     * 图文消息中的图片URL地址
     */
    private String pictureUrl;

	public String getNumberCode() {
		return numberCode;
	}

	public void setNumberCode(String numberCode) {
		this.numberCode = numberCode;
	}

	public String getAgentId() {
		return agentId;
	}

	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getMessageCode() {
		return messageCode;
	}

	public void setMessageCode(String messageCode) {
		this.messageCode = messageCode;
	}

	public Map<String, String> getMessageParam() {
		return messageParam;
	}

	public void setMessageParam(Map<String, String> messageParam) {
		this.messageParam = messageParam;
	}

	public String getMessageContent() {
		return messageContent;
	}

	public void setMessageContent(String messageContent) {
		this.messageContent = messageContent;
	}

	public String getMessageTitle() {
		return messageTitle;
	}

	public void setMessageTitle(String messageTitle) {
		this.messageTitle = messageTitle;
	}

	public String getMessageUrl() {
		return messageUrl;
	}

	public void setMessageUrl(String messageUrl) {
		this.messageUrl = messageUrl;
	}

	public String getMessageType() {
		return messageType;
	}

	public void setMessageType(String messageType) {
		this.messageType = messageType;
	}

	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	public String getCustomerStaffNameOrAgentOwner() {
		return customerStaffNameOrAgentOwner;
	}

	public void setCustomerStaffNameOrAgentOwner(String customerStaffNameOrAgentOwner) {
		this.customerStaffNameOrAgentOwner = customerStaffNameOrAgentOwner;
	}

	public String getPictureUrl() {
		return pictureUrl;
	}

	public void setPictureUrl(String pictureUrl) {
		this.pictureUrl = pictureUrl;
	}
}
