package com.sungoin.cti.server.service;

import javax.activation.CommandMap;
import javax.activation.MailcapCommandMap;
import javax.annotation.Resource;
import javax.mail.internet.MimeMessage;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

@Service
public class MessageService {
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = LoggerFactory
        .getLogger(MessageService.class);

    @Resource
    private JavaMailSender mailSender;

    /**
     * spring 发送邮件.
     *
     * @param subject the subject
     * @param target the target
     * @param content the content
     */
    public void sendMail(String subject, String content, String... target) {
        try {
            MimeMessage msg = this.mailSender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(msg, false,
                "UTF-8");
            //设置发送人
            messageHelper.setFrom("<EMAIL>", "CTI预警通知");
            //主题
            messageHelper.setSubject(subject);
            //接收者
            messageHelper.setTo(target);

            //发送内容
            messageHelper.setText(content, true);

            MailcapCommandMap mc = (MailcapCommandMap) CommandMap
                .getDefaultCommandMap();
            mc.addMailcap("text/html;; x-java-content-handler=com.sun.mail.handlers.text_html");
            mc.addMailcap("text/xml;; x-java-content-handler=com.sun.mail.handlers.text_xml");
            mc.addMailcap("text/plain;; x-java-content-handler=com.sun.mail.handlers.text_plain");
            mc.addMailcap("multipart/*;; x-java-content-handler=com.sun.mail.handlers.multipart_mixed");
            mc.addMailcap("message/rfc822;; x-java-content- handler=com.sun.mail.handlers.message_rfc822");
            CommandMap.setDefaultCommandMap(mc);
            //发送
            this.mailSender.send(msg);
        } catch (Exception e) {
            LOGGER.error("spring mail send error", e);
        }
    }
}
