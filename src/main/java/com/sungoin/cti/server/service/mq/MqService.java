/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.server.service.mq;

import com.alibaba.fastjson.JSON;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.SendResult;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service
public class MqService {
    private static final Logger log = LoggerFactory.getLogger(MqService.class);
    
    @Resource
    @Qualifier("PlatformMessageProducer")
    private Producer mqProducer;
    
    public void sendErrorMessage(String content, Exception e) {
        log.info("开始发送异常消息，content={}", content);
    	SystemMessageDto dto=new SystemMessageDto();
		dto.setMessageContent(content);
        dto.setMessageTitle("cti 运行异常");
		Message msg = new Message(MqConstants.ERROR_MESSAGE_TOPIC, MqConstants.EXCEPTION_LOG_TAG, JSON.toJSONString(dto).getBytes());
		SendResult sendResult =mqProducer.send(msg);
		log.info("send error-messageid:{},topic:{}", sendResult.getMessageId(), sendResult.getTopic());
    }
}
