package com.sungoin.cti.server.service;

import java.util.Calendar;
import java.util.Date;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import com.sungoin.cti.server.config.CommonSetting;
import com.sungoin.cti.server.scoket.CTIRemoteReq;
import com.sungoin.cti.server.scoket.CTIServer;
import com.sungoin.cti.server.scoket.EventType;
import com.sungoin.cti.server.scoket.MinaTimeServer;
import com.sungoin.cti.util.ConcurrentHelper;
import com.sungoin.cti.util.DateTimeUtil;
import com.sungoin.cti.util.Des;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;

@Service
public class ProcessService {
	public static final Logger log = LoggerFactory.getLogger(ProcessService.class);
	/**
	 * Logger for this class
	 */
	private static final Logger LOGGER = LoggerFactory
			.getLogger(ProcessService.class);

	@Resource
	private CTIServer ctiServer;

	@Resource
	private CommonSetting commonSetting;

	@Autowired
	private MessageService messageService;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	/**
	 * 检查中继通道状态是否异常.
	 */
	public void checkTrunkState() {
		//调用引擎接口获取中继通道是否异常
		boolean flag = false;
		if (flag) {
			String platform = this.commonSetting.getPlatformName();
			if (StringUtils.isNotEmpty(platform)) {
				try {
					platform = new String(platform.getBytes("iso-8859-1"), "utf-8");
				} catch (UnsupportedEncodingException ex) {
					LOGGER.error(ex.getMessage(), ex);
					platform = "";
				}
			}
			String subject = platform + "中继通道状态异常报告";
			String content = "时间: " + DateTimeUtil.formatDateTime(new Date())
					+ ",对应平台: " + this.commonSetting.getPlatformId() + ",中继通道状态异常";
			String[] personal = this.commonSetting.getPersonalMail().split(",");
			this.messageService.sendMail(subject, content, personal);
		}
	}

	/**
	 * 检查中继通道使用率.
	 */
	public void checkTrunkUsageRate() {
		DeviceStatistic trunk1 = this.ctiServer
				.getDeviceStatistic(DeviceType.TRUNK);
		this.saveTrunkUsage(trunk1);
		float currentUsageRate = (trunk1.getOpened() - trunk1.getFree())
				/ (float) trunk1.getOpened() * 100;
		LOGGER.debug("检查中继通道使用率:{}", trunk1.toString());
		if (currentUsageRate > this.commonSetting.getUsageRate()) {
			String platform = this.commonSetting.getPlatformName();
			if (StringUtils.isNotEmpty(platform)) {
				try {
					platform = new String(platform.getBytes("iso-8859-1"), "utf-8");
				} catch (UnsupportedEncodingException ex) {
					LOGGER.error(ex.getMessage(), ex);
					platform = "";
				}
			}
			final String subject = platform + "中继通道使用率较高报告";
			final String content = "时间: "
					+ DateTimeUtil.formatDateTime(new Date()) + ",对应平台: "
					+ this.commonSetting.getPlatformId() + ",百分数值: "
					+ (int) currentUsageRate;
			ConcurrentHelper.doInBackground(new Runnable() {
				@Override
				public void run() {
					String[] personal = ProcessService.this.commonSetting
							.getPersonalMail().split(",");
					ProcessService.this.messageService.sendMail(subject,
							content, personal);
				}
			});
		}
	}

	/**
	 * 记录每通电话打进来或者打出去的时候是第几根中继上的第几个时序.
	 *
	 * @param data 事件信息
	 * @param callType 呼叫类型：0:呼入,1:外呼
	 */
	public void saveCallData(String caller, String callee, int mode,
			int channel, int callType, String originalNo, String customerNo) {
		String insertSql = "insert into " + this.getTableName()
				+ " (caller,callee,call_type,"
				+ " customer_no,original_no,call_time,"
				+ " create_time,mode,channel)"
				+ " values(?,?,?,?,?,now(),now(),?,?)";
		try {
			caller = Des.getInstance().encrypt(caller);
		} catch (Exception ex) {
			log.error("加密主叫号码失败！", ex);
		}
		this.jdbcTemplate.update(insertSql, new Object[]{caller, callee,
			callType, customerNo, originalNo, mode, channel});
	}

	public void createTable(String tableName) {
		StringBuilder sql = new StringBuilder();
		sql.append("CREATE TABLE ").append(tableName);
		sql.append("(id int(11) not NULL auto_increment,");
		sql.append("caller VARCHAR(50),");
		sql.append("callee VARCHAR(50),");
		sql.append("call_type int (50),");
		sql.append("customer_no VARCHAR(50),");
		sql.append("original_no VARCHAR(50),");
		sql.append("call_time datetime,");
		sql.append("create_time datetime,");
		sql.append("channel int,");
		sql.append("mode int,PRIMARY KEY ( id ))");
		this.jdbcTemplate.execute(sql.toString());
	}

	public boolean tableExist(String tableName) {
		String querySql = "SELECT COUNT(1) FROM information_schema.tables WHERE table_name = ? and table_schema= ?";
		int count = this.jdbcTemplate.queryForObject(querySql, new Object[]{
			tableName, "cti_server"}, Integer.class);
		return count > 0;
	}

	public String getTableName() {
		return "orig_talk_note_" + DateTimeUtil.formatShortDate(new Date());
	}

	public String getNextDayTableName() {
		Calendar now = Calendar.getInstance();
		now.add(Calendar.DATE, 1);
		return "orig_talk_note_" + DateTimeUtil.formatShortDate(now.getTime());
	}

	public void saveTrunkUsage(DeviceStatistic trunk) {
		String insertSql = "insert into t_base_device_trunk(total_trunk,opend_trunk,"
				+ "used_trunk,free_trunk,platform_id,create_time)values(?,?,?,?,?,now())";
		this.jdbcTemplate.update(insertSql, new Object[]{trunk.getTotal(),
			trunk.getOpened(), trunk.getUsed(), trunk.getFree(),
			this.commonSetting.getPlatformId()});
	}

	public void resetAgentStatus() {
		String sql = "use netphone_local;";
		String sql2 = "update t_base_bind_phone set `status`='idle' where `status`='connect'";
		String sql3 = "use cti_server;";
		this.jdbcTemplate.batchUpdate(sql, sql2, sql3);
	}

	public void addConnectTrunk(String callerId, String threadName, int callType) {
		String sql = "INSERT INTO `t_base_connect_trunk` (`callId`, `threadName`,`callType`) VALUES (?, ?, ?);";
		this.jdbcTemplate.update(sql, callerId, threadName, callType);
	}

	public void removeConnectTrunk(String callerId) {
		String sql = "delete from `t_base_connect_trunk` where `callId` = ?";
		this.jdbcTemplate.update(sql, callerId);
	}

	public void resetConnectTrunk() {
		try {
			log.info("reset ConnectTrunk....");
			String sql = "select callId,threadName from `t_base_connect_trunk`";
			List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql);
			List<String> messages = new ArrayList();
			for (Map<String, Object> data : dataList) {
				String callId = (String) data.get("callId");
				String[] array = callId.split("_");
				String threadName = (String) data.get("threadName");
				String deviceId = array[0];
				int lsh = Integer.valueOf(array[1]);
                if(lsh > 0) {
                    CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.ONHOOK, 1,
						deviceId, threadName, lsh);
                    messages.add(req.getSendMessage());
                }
			}
			if(messages.size() > 0) {
				MinaTimeServer.getInstance().broadcastMessageWhenConnected(messages);
                sql = "delete from t_base_connect_trunk";
                jdbcTemplate.execute(sql);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
    
    public void cleanConnectTrunk() {
		try {
			log.info("clean ConnectTrunk....");
			String sql = "select callId,threadName from `t_base_connect_trunk`";
			List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql);
			List<String> messages = new ArrayList();
			for (Map<String, Object> data : dataList) {
				String callId = (String) data.get("callId");
				String[] array = callId.split("_");
				String threadName = (String) data.get("threadName");
				String deviceId = array[0];
				int lsh = Integer.valueOf(array[1]);
                if(lsh > 0) {
                    CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.ONHOOK, 1,
						deviceId, threadName, lsh);
                    messages.add(req.getSendMessage());
                }
			}
			if(messages.size() > 0) {
				MinaTimeServer.getInstance().broadcastMessage(messages);
                sql = "delete from t_base_connect_trunk";
                jdbcTemplate.execute(sql);
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
	}
    
    public void resetConnectTrunk(Date resetTime) {
		try {
			log.info("手动重置通话中状态，time={}", resetTime);
			String sql = "select callId,threadName from `t_base_connect_trunk` where connectTime <= ?";
			List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql, resetTime);
			List<String> messages = new ArrayList();
			for (Map<String, Object> data : dataList) {
				String callId = (String) data.get("callId");
                log.info("重置的callId：{}", callId);
				String[] array = callId.split("_");
				String threadName = (String) data.get("threadName");
				String deviceId = array[0];
				int lsh = Integer.valueOf(array[1]);
                if(lsh > 0) {
                    CTIRemoteReq req = CTIRemoteReq.getEvent(CTIRemoteReq.MSG_TYPE_JSON, EventType.ONHOOK, 1,
						deviceId, threadName, lsh);
                    messages.add(req.getSendMessage());
                }
			}
			if(messages.size() > 0) {
				MinaTimeServer.getInstance().broadcastMessageWhenConnected(messages);
			}
			sql = "delete from t_base_connect_trunk where connectTime <= ?";
			jdbcTemplate.update(sql, resetTime);
		} catch (Exception ex) {
			ex.printStackTrace();
		}

	}
	
	public Map<String, Object> queryOriginalNo(String originalNo) {
		String sql = "select redirecting_number as redirect, original from original_call where original_no=?";
		List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql, originalNo);
		return dataList.isEmpty() ? null : dataList.get(0);
	}
    
    public void checkConnectedCall(int maxSeconds) {
        String sql = "select callId from `t_base_connect_trunk` WHERE TIMESTAMPDIFF(SECOND,connectTime,NOW()) >= ?";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql, maxSeconds);
        for(Map<String, Object> data : list) {
            String callId = (String) data.get("callId");
            String[] tmp = callId.split("_");
            int lsh = Integer.parseInt(tmp[1]);
            removeConnectTrunk(callId);
            if(lsh > 0) {
                log.debug("查到超长通话，强制挂机，DeviceID：{}", callId);
                try {
                    this.ctiServer.onHook(callId);
                } catch (Exception e) {
                    log.error(e.getMessage());
                }
            }
        }
    }
	
    public void checkCallTime() {
        String maxSeconds = commonSetting.getCallMaxSeconds();
        if(StringUtils.isEmpty(maxSeconds)) {
            log.info("未配置通话超长时间！忽略检查");
        } else {
            int seconds = Integer.valueOf(maxSeconds);
            checkConnectedCall(seconds);
        }
    }
    
    public void cleanInvalidKeyUp() {
        this.ctiServer.cleanInvalidKeyUp();
    }
}
