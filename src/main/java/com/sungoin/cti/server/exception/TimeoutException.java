/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.exception;

import com.sungoin.cti.exception.CTICheckedException;

/**
 *
 * <AUTHOR> 2015-5-5
 */
public class TimeoutException extends CTICheckedException {

	private static final long serialVersionUID = 6527997033661669201L;

	public TimeoutException() {
		super();
	}

	public TimeoutException(String msg) {
		super(msg);
	}

	public TimeoutException(Throwable root) {
		super(root);
	}

	public TimeoutException(String msg, Throwable cause) {
		super(msg, cause);
	}
}
