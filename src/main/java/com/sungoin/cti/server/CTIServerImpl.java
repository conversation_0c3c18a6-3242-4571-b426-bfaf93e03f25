/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server;

import com.sungoin.cti.util.ConcurrentHelper;
import com.sungoin.cti.server.scoket.Configuration;
import com.sungoin.cti.server.scoket.CtiState;
import com.sungoin.cti.server.scoket.CTIServer;
import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.ConferenceMode;
import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import com.sungoin.cti.engine.LineState;
import com.sungoin.cti.engine.MakeCallType;
import com.sungoin.cti.engine.exception.ConferenceFullException;
import com.sungoin.cti.engine.exception.IllegalFilePathExcaption;
import com.sungoin.cti.engine.exception.TrunkDeviceFullException;
import com.sungoin.cti.engine.exception.VoiceDeviceFullException;
import com.sungoin.cti.server.config.CommonSetting;
import com.sungoin.cti.server.scoket.AbstractMessageHandler;
import com.sungoin.cti.server.scoket.MinaTimeServer;
import com.sungoin.cti.util.LshData;
import com.sungoin.cti.util.SpringHelper;
import com.sungoin.cti.server.scoket.VocHelper;
import com.sungoin.cti.server.service.ProcessService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR>
 */
@Service(value = "ctiServer")
public class CTIServerImpl implements CTIServer {

	private Date startTime;

	private static final Logger log = LoggerFactory.getLogger(CTIServerImpl.class);
    
	@Resource
	private CTIEngine engine;

	@Resource
	private ProcessService ps;

	@Resource
	private CommonSetting settings;

	@Override
	public void start() {
		//启动engine引擎
		engine.start();
		engine.init(new CTIEventHandlerAdapt(this));
		log.info("cti server start successful...");
		//启动mina
		MinaTimeServer minaTimeServer = MinaTimeServer.getInstance();
		minaTimeServer.initServer();
		log.info("mina server start successful...");
		startTime = new Date();
	}

	@Override
	public void stop() {
		log.warn(" ctiServer  stop ....");
		//停止引擎
		engine.shotdown();
		//停止mina
		MinaTimeServer.getInstance().stopServer();
		//停止线程池
		ConcurrentHelper.destory();
		//停止消息接收线程
		AbstractMessageHandler msgHandler = (AbstractMessageHandler) SpringHelper.getSpringBean(Configuration.getProperty(Configuration.DEFAULT_MESAGEHANDLER));
		msgHandler.destory();
	}

	@Override
	public int answer(String callDeviceId, String threadName, int lsh) {
		log.debug(" answer .... deviceId is {} threadName is {} lsh is {}", callDeviceId, threadName, lsh);
		int reult;
		try {
			reult = engine.answer(callDeviceId);
			LshData.add(lsh, threadName, LshData.LshType.CALL);
		} catch (VoiceDeviceFullException ex) {
			reult = CtiState.CTI_VOICE_FULL;
			log.error(ex.getMessage(), ex);
		}
		return reult;
	}

	@Override
	public String[] makeCall(String caller, String callee) {
		return makeCall(caller, callee, null);
	}

	@Override
	public String[] makeCall(String caller, String callee, String origCallee) {
		log.debug(" makeCall   caller is {} callee is {} origCallee is {}", caller, callee, origCallee);
		String errorMsg = "1";
		try {
			int inLineLength = Integer.parseInt(Configuration.getProperty("inline.dn.length"));
			MakeCallType type;
			if (inLineLength == callee.length()) {
				type = MakeCallType.INLINE;
				callee = (Integer.parseInt(callee) - Integer.parseInt(Configuration.getProperty("inline.dn.number"))) + "";
			} else {
				type = MakeCallType.OUTLINE;
				//判断被叫是否要加前缀
				if (StringUtils.isNotEmpty(settings.getCalleePrefix())) {
					log.debug("平台配置了被叫前缀：" + settings.getCalleePrefix());
					callee = settings.getCalleePrefix() + callee;
				}
			}
			String fixedOrigCallee = null;
			String directNo = null;
			if (origCallee != null) {
				Map<String, Object> data = this.ps.queryOriginalNo(origCallee);
				if (data != null) {
					//处理特殊原被叫和改发号码
					log.debug("根据小号：{} 查询到指定改发号码和原被叫，使用数据库配置。", origCallee);
					fixedOrigCallee = (String) data.get("original");
					directNo = (String) data.get("redirect");
				} else {
					//处理全局原始被叫及改发号码前缀
					log.debug("小号：{} 使用全局配置改发号码和原被叫。", origCallee);
					fixedOrigCallee = StringUtils.isNotEmpty(settings.getAllwayCaller()) ? settings.getAllwayCaller() : origCallee;
					log.debug("处理完全局小号后的原始被叫为：{}", fixedOrigCallee);
					directNo = fixedOrigCallee;
					String prefix = settings.getPrefix();
					if (StringUtils.isNotEmpty(prefix)) {
						log.debug("cti配置的全局前缀为：{}", prefix);
						String[] prefixs = prefix.split(":");
						String origPrefix = prefixs[0];
						if (origPrefix.startsWith("-")) {
							int bit = Integer.valueOf(origPrefix.substring(1));
							fixedOrigCallee = fixedOrigCallee.substring(bit);
						} else {
							fixedOrigCallee = prefixs[0] + fixedOrigCallee;
						}
						if (prefixs.length > 1) {
							String directPrefix = prefixs[1];
							if (directPrefix.startsWith("-")) {
								int bit = Integer.valueOf(directPrefix.substring(1));
								directNo = directNo.substring(bit);
							} else {
								directNo = prefixs[1] + directNo;
							}
						}
						log.debug("添加了前缀后的原始被叫：{},改发号码：{}", fixedOrigCallee, directNo);
					}
				}
			}

			final String[] data = engine.makeCall(type, caller, callee, fixedOrigCallee, directNo);
			if (data != null) {
				this.saveOrigTalkNote(caller, callee, Integer.valueOf(data[2]), Integer.valueOf(data[3]),
						1, origCallee, null);
			}
			return data;
		} catch (VoiceDeviceFullException ex) {
			log.error(ex.getMessage(), ex);
			errorMsg = CtiState.CTI_VOICE_FULL + "";
		} catch (TrunkDeviceFullException ex) {
			log.error(ex.getMessage(), ex);
			errorMsg = CtiState.CTI_DEVICE_FULL + "";
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			errorMsg = CtiState.UNKNOWN_FULL + "";
		}
		String[] data = {null, null, errorMsg};
		return data;
	}

	@Override
	public int play(String callDeviceId, String fileName, boolean loop, boolean isQueue, int maxSecond) {
		log.debug(" play music deviceId is {} fileName is {} loop is {} isQueue is {} ", callDeviceId, fileName, loop, isQueue);
		try {
			if (fileName.contains(",")) {
				return engine.playList(callDeviceId, fileName.split(","));
			}
			return engine.play(callDeviceId, fileName, loop, isQueue, maxSecond);
		} catch (IllegalFilePathExcaption ex) {
			log.error(ex.getMessage(), ex);
			return CtiState.CTI_FILEPATH_ERROR;
		}
	}

	@Override
	public int stopPlay(String callDeviceId) {
		log.debug(" stopPlay callDeviceId is ... {} ", callDeviceId);
		return engine.stopPlay(callDeviceId);
	}

	@Override
	public void receiveDTMF(String callDeviceId) {
		log.debug(" receiveDTMF  callDeviceId is...{} ", callDeviceId);
		KeyUp.initDtfm(callDeviceId, null);

	}

	@Override
	public void stopDTMF(String callDeviceId) {
		log.debug(" stopDTFM is {}", callDeviceId);
		KeyUp.cleanDtmf(callDeviceId);
	}

	@Override
	public int record(String callDeviceId, String fileName, int length, boolean isAppend) {
		try {
			log.debug(" record callDeviceId is {} fileName is {} length is {} isAppend is {}", callDeviceId, fileName, length, isAppend);
			return engine.record(callDeviceId, fileName, length, isAppend);
		} catch (IllegalFilePathExcaption ex) {
			log.error(ex.getMessage(), ex);
			return CtiState.CTI_FILEPATH_ERROR;
		} catch(Exception e) {
            log.error(e.getMessage(), e);
            return CtiState.UNKNOWN_FULL;
        }
	}

	@Override
	public int stopRecord(String callDeviceId) {
		log.debug(" stopRecord callDeviceId is {}", callDeviceId);
		return engine.stopRecord(callDeviceId);
	}

	@Override
	public int alert(String callDeviceId, String threadName, int lsh) {
		log.debug(" alert callDeviceId is {},threadName is {},lsh is {}", callDeviceId, threadName, lsh);
		int reult;
		try {
            LshData.add(lsh, threadName, LshData.LshType.CALL);
			reult = engine.alert(callDeviceId);
		} catch (VoiceDeviceFullException ex) {
			reult = CtiState.CTI_VOICE_FULL;
			log.error(ex.getMessage(), ex);
		}
		return reult;
	}

	@Override
	public int onHook(String callDeviceId) {
		log.debug(" onHook callDeviceId is {} ", callDeviceId);
		return engine.onHook(callDeviceId);
	}

	@Override
	public int connectCall(String callerDeviceID, String calleeDeviceID) {
		log.debug(" connectCall callerDeviceID is {}  calleeDeviceID is {}", callerDeviceID, calleeDeviceID);
		return engine.connectCall(callerDeviceID, calleeDeviceID);
	}
    
    @Override
	public int connectCall(String callerDeviceID, String calleeDeviceID, boolean sameDsp) {
		log.debug(" connectCall callerDeviceID is {}  calleeDeviceID is {} sameDsp is {}", callerDeviceID, calleeDeviceID, sameDsp);
		return engine.connectCall(callerDeviceID, calleeDeviceID, sameDsp);
	}

	@Override
	public int disConnectCall(String callerDeviceID, String calleeDeviceID) {
		log.debug(" disConnectCall callerDeviceID is {}  calleeDeviceID is {}", callerDeviceID, calleeDeviceID);
		return engine.disConnectCall(callerDeviceID, calleeDeviceID);
	}

	@Override
	public int resetDevice(String deviceId) {
		log.debug(" resetDevice deviceId is {}", deviceId);
		engine.resetDevice(DeviceType.TRUNK, deviceId);
		return 1;
	}

	@Override
	public int playNumberList(String deviceId, String numbers) {
		log.debug(" playNumberList deviceId is {}  numbers is {}", deviceId, numbers);
		List<String> list = new ArrayList<String>();
		char[] data = numbers.toCharArray();
		for (int i = 0; i < data.length; i++) {
			list.add(VocHelper.getCharVoc(data[i]));
		}
		try {
			return engine.playList(deviceId, list.toArray(new String[0]));
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			return CtiState.PLAY_VOICE_FULL;
		}
	}

	@Override
	public String joinConf(String callDeviceId, ConferenceMode mode, String confDeviceID) {
		try {
			log.debug(" JoinConf ...callDeviceId is {} mode is {} confDeviceID is ", callDeviceId, mode, confDeviceID);
			return engine.joinConf(callDeviceId, mode, confDeviceID);
		} catch (VoiceDeviceFullException ex) {
			log.error(ex.getMessage(), ex);
			return CtiState.CTI_VOICE_FULL + "";
		} catch (ConferenceFullException ex) {
			log.error(ex.getMessage(), ex);
			return CtiState.CONFERENCE_FULL + "";
		}
	}

	@Override
	public String createAndJoinConf(String callDeviceId, ConferenceMode mode) {
		try {
			log.debug(" createAndJoinConf ...callDeviceId is {} mode is {}", callDeviceId, mode);
			return engine.createAndJoinConf(callDeviceId, mode);
		} catch (VoiceDeviceFullException ex) {
			log.error(ex.getMessage(), ex);
			return CtiState.CTI_VOICE_FULL + "";
		} catch (ConferenceFullException ex) {
			log.error(ex.getMessage(), ex);
			return CtiState.CONFERENCE_FULL + "";
		}
	}

	@Override
	public int getDeviceState(String deviceId) {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public DeviceStatistic getDeviceStatistic(DeviceType dt) {
		return engine.getDeviceStatistic(dt);
	}

	@Override
	public List<DeviceStatistic> getAllDeviceStatistic() {
		return engine.getAllDeviceStatistic();
	}

	@Override
	public int leaveConf(String callDeviceId) {
		return engine.leaveConf(callDeviceId);
	}

	@Override
	public Date getStartTime() {
		return startTime;
	}

	@Override
	public int playDtmf(String callDeviceId, String dtmfStr) {
		return engine.playDtmf(callDeviceId, dtmfStr);
	}

	@Override
	public int getLicenseDays() {
		return engine.getLicenseDays();
	}

	@Override
	public String getTrunkLineState() {
		List<LineState> list = engine.getTrunkLineState();
		Collections.sort(list);
		StringBuilder sb = new StringBuilder();
		for (LineState state : list) {
			sb.append(state.toString()).append("\n");
		}
		return sb.toString();
	}

	@Override
	public void saveOrigTalkNote(final String caller, final String callee, final int mode,
			final int channel, final int callType, final String originalNo, final String customerNo) {
		ConcurrentHelper.doInBackground(new Runnable() {
			@Override
			public void run() {
				ps.saveCallData(caller, callee, mode, channel, callType, originalNo, customerNo);
			}
		});
	}

	@Override
	public void addConnectCall(String callId, String threadName, int callType) {
		ps.addConnectTrunk(callId, threadName, callType);
	}

	@Override
	public void removeConnectCall(String callId) {
		ps.removeConnectTrunk(callId);
	}

    @Override
    public void cleanInvalidKeyUp() {
        log.debug("cleanInvalidKeyUp...");
        KeyUp.cleanInvalidKeyUp();
    }

    @Override
    public int broadCast(String callDeviceId, String fileName) {
        log.debug(" broadCast deviceId is {} fileName is {} ", callDeviceId, fileName);
		return engine.broadCast(callDeviceId, fileName);
    }

    @Override
    public int detectSpeech(String callDeviceId, String file, long timeout) {
        log.debug(" detectSpeech deviceId is {} file is {} timeout is {}", callDeviceId, file, timeout);
        return engine.detectSpeech(callDeviceId, file, timeout);
    }

    @Override
    public int stopDetectSpeech(String callDeviceId) {
        log.debug(" stopDetectSpeech deviceId is {}", callDeviceId);
        return engine.stopDetectSpeech(callDeviceId);
    }
}
