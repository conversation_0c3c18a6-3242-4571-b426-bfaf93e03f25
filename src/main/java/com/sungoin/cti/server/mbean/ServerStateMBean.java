/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.mbean;

import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import com.sungoin.cti.server.scoket.CTIServer;
import com.sungoin.cti.server.scoket.Configuration;
import com.sungoin.cti.server.scoket.AbstractMessageHandler;
import com.sungoin.cti.util.SpringHelper;
import java.text.SimpleDateFormat;
import javax.annotation.Resource;
import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedOperationParameter;
import org.springframework.jmx.export.annotation.ManagedOperationParameters;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> 2015-5-19
 */
@Service
@ManagedResource(objectName = "ctiMBeans:name=ServerStateMBean", description = "ServerState Managed Bean")
public class ServerStateMBean {

    @Resource
    private CTIServer ctiServer;

    @ManagedOperation(description = "get Queue or threadPool used numbers ")
    public String getMaxCapacity() {
        AbstractMessageHandler msgHandler = (AbstractMessageHandler) SpringHelper.getSpringBean(Configuration.getProperty(Configuration.DEFAULT_MESAGEHANDLER));
        return "max :" + msgHandler.getMaxCapacity() + " current :" + msgHandler.getCurrentCapacity();
    }

    @ManagedOperation(description = "get device statistic")
    public String getTrunkStatistic() {
        DeviceStatistic trunk1 = ctiServer.getDeviceStatistic(DeviceType.TRUNK);
        DeviceStatistic trunk2 = ctiServer.getDeviceStatistic(DeviceType.VOC);
        DeviceStatistic trunk3 = ctiServer.getDeviceStatistic(DeviceType.CONF);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        return "启动时间为：" + format.format(ctiServer.getStartTime()) + "\nTRUNK [" + trunk1 + "] \nVOC [" + trunk2 + "] \nCONF [" + trunk3 + "]";
    }
	
	@ManagedOperation(description = "get device line state")
    public String getTrunkLineState() {
        return ctiServer.getTrunkLineState();
    }

    @ManagedOperation(description = "set ProcessTime ")
    @ManagedOperationParameters({
    @ManagedOperationParameter(name = "type", description = "type value is on/off")})
    public String setProcessTime(String type) {
        if ("off".equals(type)) {
            AbstractMessageHandler.setShowProcessTime(false);  
            return "showProcessTime is set to: off";
        } else if ("on".equals(type)) {
            AbstractMessageHandler.setShowProcessTime(true);
            return "showProcessTime is set to: on";
        }
        return "unknow type:" + type + ",please input on/off";
    }

	@ManagedOperation(description = "get license days")
    public String getLicenseDays() {
        return ctiServer.getLicenseDays() + "";
    }
}
