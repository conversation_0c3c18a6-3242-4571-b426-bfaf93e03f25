/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.server.mbean;

import java.util.Arrays;
import java.util.List;
import org.apache.logging.log4j.Level;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.core.LoggerContext;
import org.springframework.jmx.export.annotation.ManagedAttribute;
import org.springframework.jmx.export.annotation.ManagedOperation;
import org.springframework.jmx.export.annotation.ManagedOperationParameter;
import org.springframework.jmx.export.annotation.ManagedOperationParameters;
import org.springframework.jmx.export.annotation.ManagedResource;
import org.springframework.stereotype.Service;

/**
 *
 * <AUTHOR> 2015-5-6
 */
@Service
@ManagedResource(objectName = "ctiMBeans:name=LogManagerBean", description = "Log Managed Bean")
public class LogMBean {

	@ManagedAttribute(description = "managed log names")
	public List<String> getManagedLogNames() {
		return Arrays.asList("com.sungoin.cti.engine.impl.djkeygoe.local", "com.sungoin.cti.engine",
				"com.sungoin.cti.server");
	}

	@ManagedOperation(description = "Get log level")
	@ManagedOperationParameters({
	@ManagedOperationParameter(name = "name", description = "The log name")})
	public String getLogLevel(String name) {
		Logger logger = LogManager.getLogger(name);
		return logger.getLevel().toString();
	}

	@ManagedOperation(description = "Set log level")
	@ManagedOperationParameters({
	@ManagedOperationParameter(name = "name", description = "The log name"),
	@ManagedOperationParameter(name = "level", description = "The log level")})
	public void setLogLevel(String name, String level) {
		LoggerContext lc = (LoggerContext) LogManager.getContext();
		lc.getLogger(name).setLevel(Level.valueOf(level));
	}
}
