/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.util;

/**
 *
 * <AUTHOR>
 */
/**
 * @Title: SpringHelper.java
 * @Package com.sj.util
 * @Description: header注释
 * <AUTHOR>
 * @version V1.0
 */
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * The Class SpringHelper.
 *
 * <AUTHOR>
 */
@Component
public class SpringHelper implements ApplicationContextAware {

    public static final String CTI_SERVER = "ctiServer";

    /**
     * The ac.
     */
    private static ApplicationContext ac;

    /**
     * The sc.
     */
    /**
     * Sets the ac.
     *
     * @param ac the new ac
     */
    public static void setAc(ApplicationContext ac) {
        SpringHelper.ac = ac;
    }

    /*
     * (non-Javadoc)
     * 
     * @see org.springframework.context.ApplicationContextAware#setApplicationContext(org.springframework.context.
     * ApplicationContext)
     */
    @Override
    public void setApplicationContext(ApplicationContext ac)  {
        setAc(ac);
    }

    /**
     * Gets the applicatoin context.
     *
     * @return the applicatoin context
     */
    public static ApplicationContext getApplicatoinContext() {
        return ac;
    }

    /**
     * Gets the spring bean.
     *
     * @param name the name
     * @return the spring bean
     */
    public static Object getSpringBean(String name) {
        if (ac == null) {
            throw new IllegalStateException("spring环境尚未启动！");
        }
        return ac.getBean(name);
    }

	 public static  <T extends Object> T getSpringBean(Class<T> type) {
        if (ac == null) {
            throw new IllegalStateException("spring环境尚未启动！");
        }
        return ac.getBean(type);
    }
}
