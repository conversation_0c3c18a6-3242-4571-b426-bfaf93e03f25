/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.util;

import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 存储设备流水号和线程名 存储会议流水号和线程名
 *
 * <AUTHOR>
 */
public final class LshData {
    
    private static final Logger log = LoggerFactory.getLogger(LshData.class);
    
    private static final Map<Integer, String> talkLshMap = new java.util.concurrent.ConcurrentHashMap<Integer, String>();
    private static final Map<Integer, String> confLshMap = new java.util.concurrent.ConcurrentHashMap<Integer, String>();
    
    public static enum LshType {
        CALL, CONF
    }

	private LshData() {
	}
	
    
    public static void add(int key, String value, LshType lshType) {
//        log.debug(" LshData add key is ...{} value is ...{} lshType is ...{}", key, value, lshType);
        if (lshType == LshType.CALL) {
            talkLshMap.put(key, value);
//            log.debug("old map size {} ,new map size {}",talkLshMap.size(),talkLshMap.size()-1);
        } else {
            confLshMap.put(key, value);
//            log.debug("old map size {} ,new map size {}",confLshMap.size(),confLshMap.size()-1);
        }
    }
    
    public static String getThreadName(int key, LshType lshType) {
        if (lshType == LshType.CALL) {
            return talkLshMap.get(key);
        } else {
            return confLshMap.get(key);
        }
        
    }
    
    public static void remove(int key, LshType lshType) {
        if (lshType == LshType.CALL) {
            talkLshMap.remove(key);
//            log.debug("removed  talkLshMap ,new map size {} key is {}",talkLshMap.size(),key);
        } else {
            confLshMap.get(key);
//            log.debug("removed confLshMap {} ,new map size {} key is {}",confLshMap.size(),key);
        }
    }
    
}
