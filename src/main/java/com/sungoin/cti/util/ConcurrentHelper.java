/*
 * To change this template, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.util;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * <p>系统名：sungoin platform
 * <p>公司名：sungoin 
 * <p>文件名：ConcurrentHelper
 * <p>主要功能：
 * <p>模块名称：
 * <p>模块编号：
 ********************************
 * <p>创建日期：2012-6-26 11:44:07
 * <p>作者：chenlei
 * <p>版本：V1.0
 *************
 * <p>修改人：
 * <p>修改日期：
 * <p>版本：V
 */
public final class ConcurrentHelper {	

	private  ConcurrentHelper() {
	}
	
	private static final ExecutorService es = Executors.newCachedThreadPool();
	
	public static void doInBackground(Runnable task) {
		es.submit(task);
	}
	
	public static <T> Future<T> doInBackground(Callable<T> task) {
		return es.submit(task);
	}
	
	public static void destory() {
		es.shutdownNow();
	}
}
