/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.util;

import com.thoughtworks.xstream.XStream;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public final class XMLHelper {

    private static final XStream xstream = new XStream();

	private XMLHelper() {
	}

    /**
     * 将Bean转换为XML
     *
     * @param clazzMap 别名-类名映射Map
     * @param bean 要转换为xml的bean对象
     * @return XML字符串
     */
    public static String bean2xml(Map<String, Class> clazzMap, Object bean) {
        for (Map.Entry<String, Class> m : clazzMap.entrySet()) {
            xstream.alias(m.getKey(), m.getValue());
        }
        String xml = xstream.toXML(bean);
        return xml;
    }

    /**
     * 将XML转换为Bean
     *
     * @param clazzMap 别名-类名映射Map
     * @param xml 要转换为bean对象的xml字符串
     * @return Java Bean对象
     */
    public static Object xml2Bean(Map<String, Class> clazzMap, String xml) {
        for (Map.Entry<String, Class> m : clazzMap.entrySet()) {
            xstream.alias(m.getKey(), m.getValue());
        }
        Object bean = xstream.fromXML(xml);
        return bean;
    }

    /**
     * 获取XStream对象
     *
     * @param clazzMap 别名-类名映射Map
     * @return XStream对象
     */
    public static XStream getXStreamObject(Map<String, Class> clazzMap) {
        for (Map.Entry<String, Class> m : clazzMap.entrySet()) {
            xstream.alias(m.getKey(), m.getValue());
        }
        return xstream;
    }

}
