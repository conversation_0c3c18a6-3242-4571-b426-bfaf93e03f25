/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.util;

import java.text.ParseException;
import java.util.Date;

import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;

/**
 * <AUTHOR> 2015-8-4
 */
public class DateTimeUtil {

    public static String format(Date date, DateTimeUtil.Pattern pattern) {
        return date == null ? null : DateFormatUtils.format(date,
            pattern.context);
    }

    public static String formatDate(Date date) {
        return date == null ? null : DateFormatUtils.format(date,
            DateTimeUtil.Pattern.DATE.context);
    }

    public static String formatShortDate(Date date) {
        return date == null ? null : DateFormatUtils.format(date,
            DateTimeUtil.Pattern.SHORTDATE.context);
    }

    public static String formatTime(Date date) {
        return date == null ? null : DateFormatUtils.format(date,
            DateTimeUtil.Pattern.TIME.context);
    }

    public static String formatShortTime(Date date) {
        return date == null ? null : DateFormatUtils.format(date,
            DateTimeUtil.Pattern.SHORTTIME.context);
    }

    public static String formatDateTime(Date date) {
        return date == null ? null : DateFormatUtils.format(date,
            DateTimeUtil.Pattern.DATETIME.context);
    }

    public static Date parseDate(String text, DateTimeUtil.Pattern pattern) {
        try {
            return DateUtils.parseDate(text, pattern.context);
        } catch (ParseException ex) {
            ex.printStackTrace();
            return null;
        }
    }

    public static enum Pattern {

        DATE("yyyy-MM-dd"), TIME("HH:mm:ss"), DATETIME("yyyy-MM-dd HH:mm:ss"), SHORTTIME(
            "HH:mm"), SHORTDATETIME("yyyy-MM-dd HH:mm"), SHORTDATE("yyyyMMdd");
        private final String context;

        private Pattern(String context) {
            this.context = context;
        }
    }
}
