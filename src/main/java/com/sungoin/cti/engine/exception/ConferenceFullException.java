/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.exception;

import com.sungoin.cti.exception.CTICheckedException;

/**
 *
 * <AUTHOR> 2015-5-6
 */
public class ConferenceFullException extends CTICheckedException {

	private static final long serialVersionUID = 6880183149534849459L;

	public ConferenceFullException() {
		super();
	}

	public ConferenceFullException(String msg) {
		super(msg);
	}

	public ConferenceFullException(Throwable root) {
		super(root);
	}

	public ConferenceFullException(String msg, Throwable cause) {
		super(msg, cause);
	}
}
