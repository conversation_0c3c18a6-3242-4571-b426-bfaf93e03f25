/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.exception;

import com.sungoin.cti.exception.CTICheckedException;

/**
 *
 * <AUTHOR> 2015-5-6
 */
public class TrunkDeviceFullException extends CTICheckedException {

	private static final long serialVersionUID = -3923887531468007894L;

	public TrunkDeviceFullException() {
		super();
	}

	public TrunkDeviceFullException(String msg) {
		super(msg);
	}

	public TrunkDeviceFullException(Throwable root) {
		super(root);
	}

	public TrunkDeviceFullException(String msg, Throwable cause) {
		super(msg, cause);
	}
}
