/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.exception;

import com.sungoin.cti.exception.CTICheckedException;

/**
 *
 * <AUTHOR> 2015-5-12
 */
public class IllegalFilePathExcaption extends CTICheckedException {

	private static final long serialVersionUID = -4404517191549160570L;

	public IllegalFilePathExcaption() {
		super();
	}

	public IllegalFilePathExcaption(String msg) {
		super(msg);
	}

	public IllegalFilePathExcaption(Throwable root) {
		super(root);
	}

	public IllegalFilePathExcaption(String msg, Throwable cause) {
		super(msg, cause);
	}
}
