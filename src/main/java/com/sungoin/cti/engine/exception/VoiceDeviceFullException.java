/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.exception;

import com.sungoin.cti.exception.CTICheckedException;

/**
 *
 * <AUTHOR> 2015-5-6
 */
public class VoiceDeviceFullException extends CTICheckedException {

	private static final long serialVersionUID = -3668765891989115057L;

	public VoiceDeviceFullException() {
		super();
	}

	public VoiceDeviceFullException(String msg) {
		super(msg);
	}

	public VoiceDeviceFullException(Throwable root) {
		super(root);
	}

	public VoiceDeviceFullException(String msg, Throwable cause) {
		super(msg, cause);
	}
}
