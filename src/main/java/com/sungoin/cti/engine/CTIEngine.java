/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine;

import com.sungoin.cti.engine.exception.ConferenceFullException;
import com.sungoin.cti.engine.exception.IllegalFilePathExcaption;
import com.sungoin.cti.engine.exception.TrunkDeviceFullException;
import com.sungoin.cti.engine.exception.VoiceDeviceFullException;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface CTIEngine extends CTIEventProcess{

	public EngineState getState();

	public void start();

	public void init(CTIEventHandler eventHandler);

	public void shotdown();
	
	public int alert(String callDeviceId) throws VoiceDeviceFullException;

	public int answer(String callDeviceId) throws VoiceDeviceFullException;

	public int play(String callDeviceId, String fileName, boolean loop, boolean isQueue, int maxSecond) throws IllegalFilePathExcaption;
    
    public int broadCast(String callDeviceId, String fileName);

	public int stopPlay(String callDeviceId);

	public int record(String callDeviceId, String fileName, int length, boolean isAppend) throws IllegalFilePathExcaption;
    
	public int stopRecord(String callDeviceId);

	public int onHook(String callDeviceId);

	public String createAndJoinConf(String callDeviceId, ConferenceMode mode) throws VoiceDeviceFullException, ConferenceFullException;
	
	public String joinConf(String callDeviceId, ConferenceMode mode, String confDeviceId) throws VoiceDeviceFullException, ConferenceFullException;
	
	public String[] makeCall(MakeCallType type, String caller, String callee) throws VoiceDeviceFullException,TrunkDeviceFullException;
	
	public String[] makeCall(MakeCallType type, String caller, String callee, String origCallee, String redirectNo) throws VoiceDeviceFullException,TrunkDeviceFullException;
	
	public int connectCall(String callerDeviceID, String calleeDeviceId);
    
    public int connectCall(String callerDeviceID, String calleeDeviceId, boolean sameDsp);
	
	public int disConnectCall(String callerDeviceID, String calleeDeviceId);
	
	public DeviceStatistic getDeviceStatistic(DeviceType dt);
	
	public List<DeviceStatistic> getAllDeviceStatistic();
	
	public int getCalleeCallNum(String callee);
	
	public void resetDevice(DeviceType devType, String deviceId);
	
	public int playList(String callDeviceId, String fileNames[]) throws IllegalFilePathExcaption ;
	
	public int leaveConf(String callDeviceId);
	
	public int getLicenseDays();
	
	public int playDtmf(String callDeviceId, String dtmfStr);
	
	public int playTone(String callDeviceId, int nPlayType);
	
	public List<LineState> getTrunkLineState();
    
    public int detectSpeech(String callDeviceId, String file, long timeout);
    
    public int stopDetectSpeech(String callDeviceId);
}
