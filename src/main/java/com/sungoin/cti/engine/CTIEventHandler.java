/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine;


/**
 *
 * <AUTHOR>
 */
public interface CTIEventHandler {

	/**
	 * 来电
	 * @param event 
	 */
	public void callIncome(CTIEvent event);
	
	/**
	 * 振铃
	 * @param event 
	 */
	public void alert(CTIEvent event);
	
	/**
	 * 摘机
	 * @param event 
	 */
	public void offHook(CTIEvent event);
	
	/**
	 * 挂机
	 * @param event 
	 */
	public void onHook(CTIEvent event);
	
	/**
	 * 呼叫返回事件
	 * @param event 
	 */
	public void callOut(CTIEvent event);
	
	/**
	 * 放音结束事件
	 * @param event 
	 */
	public void playEnd(CTIEvent event);
	
	/**
	 * 收码事件
	 * @param event 
	 */
	public void receiveDTMF(CTIEvent event);
	
	/**
	 * 录音结束事件
	 * @param event 
	 */
	public void recordEnd(CTIEvent event);
	
	/**
	 * 加入会议事件
	 * @param event 
	 */
	public void joinToConf(CTIEvent event);
	
	/**
	 * 离开会议事件
	 * @param event 
	 */
	public void leaveConf(CTIEvent event);
	
	/**
	 * 会议结束
	 * @param event 
	 */
	public void clearConf(CTIEvent event);
    
    /**
     * 话单
     * @param event 
     */
    public void cdr(CTIEvent event);
    
    /**
     * 语音识别
     * @param event 
     */
    public void detectSpeech(CTIEvent event);
}
