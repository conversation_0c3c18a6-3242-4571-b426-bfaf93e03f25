/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.engine;

/**
 * 
 * <AUTHOR>
 * 2015-11-22
 */
public class LineState implements Comparable<LineState>{
	private int model;
	private int channel;
	private String lineState;

	public LineState(int model, int channel, String lineState) {
		this.model = model;
		this.channel = channel;
		this.lineState = lineState;
	}
	
	public int getModel() {
		return model;
	}

	public void setModel(int model) {
		this.model = model;
	}

	public int getChannel() {
		return channel;
	}

	public void setChannel(int channel) {
		this.channel = channel;
	}

	public String getLineState() {
		return lineState;
	}

	public void setLineState(String lineState) {
		this.lineState = lineState;
	}

	@Override
	public String toString() {
		return "LineState{" + "model=" + model + ", channel=" + channel + ", lineState=" + lineState + '}';
	}

	@Override
	public int compareTo(LineState o) {
		int m = this.model - o.model;
		return m == 0 ? this.channel - o.channel : m;
	}
}
