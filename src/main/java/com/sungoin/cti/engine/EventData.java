/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine;

/**
 *
 * <AUTHOR> 2015-5-4
 */
public class EventData {

	private String deviceID;//主设备
	private String usedDeviceID;//次设备
	private final long currentTime = System.currentTimeMillis();
	private int state;
	private int errorCode;
	private int taskId;
	private int confMembers;
	private String dtmf;
	private String caller;
	private String callee;
	private int lsh;
	private boolean callOver;
	private String mode;
	private int stopCode;
	private String module;
	private String channel;
    private String record;
    private String text;

	public boolean isCallOver() {
		return callOver;
	}

	public void setCallOver(boolean callOver) {
		this.callOver = callOver;
	}

	public String getDtmf() {
		return dtmf;
	}

	public void setDtmf(String dtmf) {
		this.dtmf = dtmf;
	}

	public int getConfMembers() {
		return confMembers;
	}

	public void setConfMembers(int confMembers) {
		this.confMembers = confMembers;
	}

	public EventData(String deviceID, int lsh, String usedDeviceID) {
		this.deviceID = deviceID;
		this.lsh = lsh;
		this.usedDeviceID = usedDeviceID;
	}

	public void setDeviceID(String deviceID) {
		this.deviceID = deviceID;
	}

	public void setUsedDeviceID(String usedDeviceID) {
		this.usedDeviceID = usedDeviceID;
	}

	public String getDeviceID() {
		return deviceID;
	}

	public String getUsedDeviceID() {
		return usedDeviceID;
	}

	public long getEventTime() {
		return currentTime;
	}

	public int getTaskId() {
		return taskId;
	}

	public void setTaskId(int taskId) {
		this.taskId = taskId;
	}

	public int getState() {
		return state;
	}

	public void setState(int state) {
		this.state = state;
	}

	public String getCaller() {
		return caller;
	}

	public void setCaller(String caller) {
		this.caller = caller;
	}

	public String getCallee() {
		return callee;
	}

	public void setCallee(String callee) {
		this.callee = callee;
	}

	public int getLsh() {
		return lsh;
	}

	public void setLsh(int lsh) {
		this.lsh = lsh;
	}

	public int getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(int errorCode) {
		this.errorCode = errorCode;
	}

	public String getMode() {
		return mode;
	}

	public void setMode(String mode) {
		this.mode = mode;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public String getChannel() {
		return channel;
	}

	public void setChannel(String channel) {
		this.channel = channel;
	}

	public int getStopCode() {
		return stopCode;
	}

	public void setStopCode(int stopCode) {
		this.stopCode = stopCode;
	}

    public String getRecord() {
        return record;
    }

    public void setRecord(String record) {
        this.record = record;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

	@Override
	public String toString() {
		return "EventData{" + "deviceID=" + deviceID + ", usedDeviceID=" + usedDeviceID
			+ ", currentTime=" + currentTime + ", lsh=" + lsh + ", state=" + state
			+ ", callOver=" + callOver + ", mode=" + mode + ", errorCode=" + errorCode + '}';
	}
}
