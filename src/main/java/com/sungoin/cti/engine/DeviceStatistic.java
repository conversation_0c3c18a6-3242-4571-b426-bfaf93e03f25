/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.engine;

/**
 *
 * <AUTHOR>
 */
public class DeviceStatistic {
	private final DeviceType type;
	private final int total;
	private final int opened;
	private final int used;
	private final int free;
	private String details;

	public DeviceStatistic(DeviceType type, int total, int opened, int used, int free) {
		this.type = type;
		this.total = total;
		this.opened = opened;
		this.used = used;
		this.free = free;
	}


	public int getTotal() {
		return total;
	}

	public int getOpened() {
		return opened;
	}

	public int getUsed() {
		return used;
	}

	public int getFree() {
		return free;
	}
	
	public String getDetails() {
		return details;
	}

	public void setDetails(String details) {
		this.details = details;
	}

	@Override
	public String toString() {
		return "DeviceStatistic{" + "type=" + type + ", total=" + total + ", opened=" + opened + ", used=" + used + ", free=" + free + ", details=" + details + '}';
	}
	
}
