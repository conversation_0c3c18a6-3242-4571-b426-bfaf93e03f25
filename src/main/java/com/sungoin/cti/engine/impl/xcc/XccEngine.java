/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.engine.impl.xcc;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.CTIEvent;
import com.sungoin.cti.engine.CTIEventHandler;
import com.sungoin.cti.engine.ConferenceMode;
import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import com.sungoin.cti.engine.EngineState;
import static com.sungoin.cti.engine.EventSource.CONF;
import static com.sungoin.cti.engine.EventSource.TRUNK;
import static com.sungoin.cti.engine.EventSource.VOC;
import static com.sungoin.cti.engine.EventType.ALERTCALL;
import static com.sungoin.cti.engine.EventType.ANSWERCALL;
import static com.sungoin.cti.engine.EventType.CALLIN;
import static com.sungoin.cti.engine.EventType.CALLOUT;
import static com.sungoin.cti.engine.EventType.CLEARCALL;
import static com.sungoin.cti.engine.EventType.CLEARCONF;
import static com.sungoin.cti.engine.EventType.DTMF;
import static com.sungoin.cti.engine.EventType.JOINTOCONF;
import static com.sungoin.cti.engine.EventType.LEAVEFROMCONF;
import static com.sungoin.cti.engine.EventType.PLAY;
import com.sungoin.cti.engine.LineState;
import com.sungoin.cti.engine.MakeCallType;
import com.sungoin.cti.engine.exception.ConferenceFullException;
import com.sungoin.cti.engine.exception.IllegalFilePathExcaption;
import com.sungoin.cti.engine.exception.InitEngineFailException;
import com.sungoin.cti.engine.exception.TrunkDeviceFullException;
import com.sungoin.cti.engine.exception.VoiceDeviceFullException;
import com.sungoin.cti.engine.impl.xcc.nats.NatsClient;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class XccEngine implements CTIEngine {
    
    private static final Logger LOG = LoggerFactory.getLogger(XccEngine.class);
    private static final String REPLACE_PATH = "/freeswitch";
    
    private final Map<String, Integer> deviceLshMap = new ConcurrentHashMap<>();
    private final Map<String, String> nodeIdMap = new ConcurrentHashMap<>();
    private final Map<String, String> deviceIdMap = new ConcurrentHashMap<>();
    private final Map<String, String> loopPlayMap = new ConcurrentHashMap<>();
    
    private NatsClient client;
    private volatile EngineState state = EngineState.STOPED;
    private CTIEventHandler eventHandler;
    private AtomicInteger lsh = new AtomicInteger(1);
    
    public XccEngine() {
        client = new NatsClient(this);
    }
    
    public int getNextLsh() {
        return lsh.getAndIncrement();
    }
    
    public Integer getDeviceLsh(String deviceId) {
        Integer lsh = deviceLshMap.get(deviceId);
        return lsh == null ? -1 : lsh;
    }
    
    public void putDeviceLsh(String deviceId, int lsh) {
        this.deviceLshMap.put(deviceId, lsh);
    }
    
    public String getNodeId(String deviceId) {
        return this.nodeIdMap.get(deviceId);
    }
   
    public void putNodeId(String deviceId, String nodeId) {
        nodeIdMap.put(deviceId, nodeId);
        deviceIdMap.put(nodeId, deviceId);
    }
    
    public void putLoopPlay(String deviceId, String file) {
        loopPlayMap.put(deviceId, file);
    }
    
    public void removeLoopPlay(String deviceId) {
        loopPlayMap.remove(deviceId);
    }
    
    public String getLoopPlay(String deviceId) {
        return loopPlayMap.get(deviceId);
    }
    
    public String getDeviceIdByNodeId(String nodeId) {
        return deviceIdMap.get(nodeId);
    }

    public void clearCall(String deviceId) {
        this.deviceLshMap.remove(deviceId);
        String nodeId = this.nodeIdMap.remove(deviceId);
        if(nodeId != null) {
            this.deviceIdMap.remove(nodeId);
        }
        loopPlayMap.remove(deviceId);
    }
    
    @Override
    public EngineState getState() {
        return state;
    }

    @Override
    public void start() {
        client = new NatsClient(this);
        state = EngineState.STARTED;
        LOG.info("xcc engine started ...");
    }

    @Override
    public void init(CTIEventHandler eventHandler) {
        this.eventHandler = eventHandler;
        state = EngineState.RUNNING;
        try {
            client.start();
        } catch (Exception e) {
            throw new InitEngineFailException(e.getMessage(), e);
        }
        LOG.info("xcc engine inited ...");
    }

    @Override
    public void shotdown() {
        client.close();
        state = EngineState.STOPED;
    }

    @Override
    public int alert(String callDeviceId) throws VoiceDeviceFullException {
        String[] ids = callDeviceId.split("_");
        client.accept(ids[0], this.getNodeId(ids[0]));
        return 1;
    }

    @Override
    public int answer(String callDeviceId) throws VoiceDeviceFullException {
        String[] ids = callDeviceId.split("_");
        client.answer(ids[0], this.getNodeId(ids[0]));
        return 1;
    }

    @Override
    public int play(String callDeviceId, String fileName, boolean loop, boolean isQueue, int maxSecond) throws IllegalFilePathExcaption {
        String[] ids = callDeviceId.split("_");
        int idx = fileName.indexOf("_Broadcast");
        if(idx > 0) {
            client.broadcast(ids[0], this.getNodeId(ids[0]), fileName.substring(0, idx));
        } else {
            client.play(ids[0], this.getNodeId(ids[0]), fileName);
            if(loop) {
                this.putLoopPlay(ids[0], fileName);
            } else {
                this.removeLoopPlay(ids[0]);
            }
        }
        return 1;
    }

    @Override
    public int stopPlay(String callDeviceId) {
        String[] ids = callDeviceId.split("_");
        client.stop(ids[0], this.getNodeId(ids[0]));
        this.removeLoopPlay(ids[0]);
        return 1;
    }

    @Override
    public int record(String callDeviceId, String fileName, int length, boolean isAppend) throws IllegalFilePathExcaption {
        //只处理留言的录音，其他录音由XCC系统实现，不受cti控制
        if(fileName.contains("boxes")) {
            String[] ids = callDeviceId.split("_");
            client.record(ids[0], this.getNodeId(ids[0]), fileName);
        }
        return 1;
    }

    @Override
    public int stopRecord(String callDeviceId) {
        //XCC系统实现，不受cti控制
        return 1;
    }

    @Override
    public int onHook(String callDeviceId) {
        String[] ids = callDeviceId.split("_");
        this.client.hangup(ids[0]);
        return 1;
    }

    @Override
    public String createAndJoinConf(String callDeviceId, ConferenceMode mode) throws VoiceDeviceFullException, ConferenceFullException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public String joinConf(String callDeviceId, ConferenceMode mode, String confDeviceId) throws VoiceDeviceFullException, ConferenceFullException {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public String[] makeCall(MakeCallType type, String caller, String callee) throws VoiceDeviceFullException, TrunkDeviceFullException {
        return this.makeCall(type, caller, callee, null, null);
    }

    @Override
    public String[] makeCall(MakeCallType type, String caller, String callee, String origCallee, String redirectNo) throws VoiceDeviceFullException, TrunkDeviceFullException {
        String deviceId = UUID.randomUUID().toString();
        int lsh = this.getNextLsh();
        this.putDeviceLsh(deviceId, lsh);
        String fixedCaller = caller;
        //判断外地手机是否把0截掉
		if (caller.startsWith("01") && caller.charAt(2) != '0' && !Configuration.CALLER_KEEP_PREFIX) {
			fixedCaller = caller.substring(1);
		}
        client.dial(deviceId, fixedCaller, callee, origCallee);
        return new String[]{lsh + "", deviceId, "0", "0"};
    }

    @Override
    public int connectCall(String callerDeviceID, String calleeDeviceId) {
        return connectCall(callerDeviceID, calleeDeviceId, false);
    }

    @Override
    public int connectCall(String callerDeviceID, String calleeDeviceId, boolean sameDsp) {
        String[] ids = callerDeviceID.split("_");
        String[] ids2 = calleeDeviceId.split("_");
        this.client.bridge(ids[0], ids2[0]);
        return 1;
    }

    @Override
    public int disConnectCall(String callerDeviceID, String calleeDeviceId) {
        String[] ids = callerDeviceID.split("_");
        String[] ids2 = calleeDeviceId.split("_");
        this.client.unBridge(ids[0], ids2[0]);
        return 1;
    }

    @Override
    public DeviceStatistic getDeviceStatistic(DeviceType dt) {
        return new DeviceStatistic(DeviceType.TRUNK, 0, 0, 0, 0);
    }

    @Override
    public List<DeviceStatistic> getAllDeviceStatistic() {
        List<DeviceStatistic> list = new ArrayList<>();
        list.add(new DeviceStatistic(DeviceType.TRUNK, 0, 0, 0, 0));
        return list;
    }

    @Override
    public int getCalleeCallNum(String callee) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public void resetDevice(DeviceType devType, String deviceId) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public int playList(String callDeviceId, String[] fileNames) throws IllegalFilePathExcaption {
        String[] ids = callDeviceId.split("_");
        StringBuilder sb = new StringBuilder("file_string://");
        for(String f : fileNames) {
            sb.append(f).append("!");
        }
        sb.deleteCharAt(sb.length() -1);
        client.play(ids[0], this.getNodeId(ids[0]), sb.toString());
        return 1;
    }

    @Override
    public int leaveConf(String callDeviceId) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public int getLicenseDays() {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public int playDtmf(String callDeviceId, String dtmfStr) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public int playTone(String callDeviceId, int nPlayType) {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public List<LineState> getTrunkLineState() {
        throw new UnsupportedOperationException("Not supported yet."); // Generated from nbfs://nbhost/SystemFileSystem/Templates/Classes/Code/GeneratedMethodBody
    }

    @Override
    public void processEvent(CTIEvent event) {
        LOG.info("engine event come : event source = {},event type = {}, event data = {}",
                event.getEventSource(), event.getEventType(), event.getEventData());
        switch (event.getEventSource()) {
            case TRUNK:
                switch (event.getEventType()) {
                    case CALLIN:
                        //来电事件
                        eventHandler.callIncome(event);
                        break;
                    case ALERTCALL:
                        //振铃事件
                        eventHandler.alert(event);
                        break;
                    case ANSWERCALL:
                        //摘机事件
                        eventHandler.offHook(event);
                        break;
                    case CLEARCALL:
                        //挂机事件
                        eventHandler.onHook(event);
                        break;
                    case CALLOUT:
                        //外呼结果事件
                        eventHandler.callOut(event);
                        break;
                    case CDR:
                        //话单事件，推送录音地址
                        event.getEventData().setRecord(event.getEventData().getRecord().replace(REPLACE_PATH, Configuration.getReplacePath()));
                        eventHandler.cdr(event);
                        break;
                    default:
                        LOG.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
                }
                break;
            case VOC:
                switch (event.getEventType()) {
                    case PLAY:
                        //放音结束事件,判断是否在循环放音，如果循环放音则不发送事件
                        String deviceId = event.getEventData().getDeviceID();
                        String file = this.getLoopPlay(deviceId);
                        if(file != null && event.getEventData().getState() == 1) {
                            try {
                                LOG.debug("循环播放文件：{}", file);
                                this.play(deviceId + "_" + event.getEventData().getLsh(), file, false, false, 0);
                            } catch (Exception e) {
                                LOG.error(e.getMessage(), e);
                            }
                        } else {
                            this.removeLoopPlay(deviceId);
                            eventHandler.playEnd(event);
                        }
                        break;
                    case DTMF:
                        //放音结束事件
                        eventHandler.receiveDTMF(event);
                        break;
                    case SPEECH:
                        //语音识别
                        eventHandler.detectSpeech(event);
                        break;
                    default:
                        LOG.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
                }
                break;
            case CONF:
                switch (event.getEventType()) {
                    case JOINTOCONF:
                        //加入会议事件
                        eventHandler.joinToConf(event);
                        break;
                    case LEAVEFROMCONF:
                        //离开会议事件
                        eventHandler.leaveConf(event);
                        break;
                    case CLEARCONF:
                        //会议结束事件
                        eventHandler.clearConf(event);
                        break;
                    default:
                        LOG.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
                }
                break;
            default:
                LOG.warn("unknow eventSource : {}", event.getEventSource());
        }
    }

    @Override
    public int broadCast(String callDeviceId, String fileName) {
        String[] ids = callDeviceId.split("_");
        client.broadcast(ids[0], this.getNodeId(ids[0]), fileName);
        return 1;
    }

    @Override
    public int detectSpeech(String callDeviceId, String file, long timeout) {
        String[] ids = callDeviceId.split("_");
        client.detectSpeech(ids[0], this.getNodeId(ids[0]), file, timeout);
        return 1;
    }

    @Override
    public int stopDetectSpeech(String callDeviceId) {
        String[] ids = callDeviceId.split("_");
        client.stopDetectSpeech(ids[0], this.getNodeId(ids[0]));
        return 1;
    }
}
