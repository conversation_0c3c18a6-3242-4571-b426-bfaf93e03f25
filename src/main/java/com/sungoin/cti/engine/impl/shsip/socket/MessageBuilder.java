/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip.socket;

import com.sungoin.cti.engine.impl.shsip.Constants;
import com.sungoin.cti.engine.impl.shsip.ShsipEngine;
import static com.sungoin.cti.engine.impl.shsip.socket.SipMessage.SPACE;

/**
 *
 * <AUTHOR>
 */
public class MessageBuilder {

    private static final SipMessage heartBeatMessage = new SipMessage(Constants.COMMAND_HEARTBEAT + "1" + ShsipEngine.MASTER_ID);

    public static SipMessage getRegisterMessage() {
        String s = Constants.COMMAND_REGISTER + ShsipEngine.MASTER_ID + SPACE + ShsipEngine.MACHINE_NO + " 1 2 3 4 5 6 7 8 9 0";
        return new SipMessage(s);
    }

    public static SipMessage getLogoutMessage() {
        String s = Constants.COMMAND_LOGOUT + ShsipEngine.MASTER_ID + SPACE + ShsipEngine.MACHINE_NO;
        return new SipMessage(s);
    }

    public static SipMessage getHeartBeatMessage() {
        return heartBeatMessage;
    }

    public static SipMessage getCallOutMessage(String caller, String callee, int lsh) {
        return getCallOutMessage(caller, callee, lsh, 30);
    }

    public static SipMessage getCallOutMessage(String caller, String callee, int lsh, int timeout) {
        //"J163 001110106868 13701836419 40400"
        if (timeout > 99) {
            timeout = 99;
        }
        String timeoutStr = String.format("%2d", timeout);
        String s = Constants.COMMAND_CALLOUT + ShsipEngine.MASTER_ID + SPACE + String.format("%03d", lsh) + "1" + caller + SPACE + callee + SPACE + timeoutStr + timeoutStr + "0";
        return new SipMessage(s);
    }

    public static SipMessage getAnswerMessage(String deviceId, boolean alertMode) {
        String s = Constants.COMMAND_ANSWER + deviceId + (alertMode ? "0" : "1");
        return new SipMessage(s);
    }

    public static SipMessage getOnhookMessage(String deviceId, Integer lsh) {
        String s = Constants.COMMAND_ONHOOK + deviceId;
        if (lsh != null) {
            s += String.format("%03d", lsh);
        }
        return new SipMessage(s);
    }

    public static SipMessage getPlayMessage(String deviceId, int playCounts, int fileCounts, String file) {
        // A999010000101/data/voice/200000000.wav 0
        String s = Constants.COMMAND_PLAY + deviceId + playCounts + String.format("%02d", fileCounts) + file + SPACE + "1" + SPACE + "A";
        return new SipMessage(s);
    }

    public static SipMessage getStopPlayMessage(String deviceId) {
        return new SipMessage(Constants.COMMAND_STOP_CONTROL + deviceId);
    }

    public static SipMessage getStopRecordMessage(String deviceId) {
        return new SipMessage(Constants.COMMAND_STOP_CONTROL + deviceId);
    }

    public static SipMessage getRecordMessage(String deviceId, int maxTime, String filePath, boolean append, boolean separate) {
        //B99901009601200005/record/2012/05/25/4000259758_20120525152648216555.wav A 1#[0a]
        String time = "9900";//默认最大值
        if (maxTime > 0) {
            time = String.format("%04d", maxTime);
        }
        return new SipMessage(Constants.COMMAND_RECORD + deviceId + time + "0005" + filePath + SPACE + "A" + SPACE + "0" + (separate ? "S" : "A") + (append ? "a" : ""));
    }

    public static SipMessage getDtmfMessage(String deviceId, int length) {
        return new SipMessage(Constants.COMMAND_DTMF + deviceId + String.format("%02d", length) + "00" + "A");
    }

    public static SipMessage getConnectMessage(String callerDeviceId, String calleeDeviceId) {
        return new SipMessage(Constants.COMMAND_CONNECT + callerDeviceId + calleeDeviceId + "0");
    }

    public static SipMessage getDisConnectMessage(String callerDeviceId, String calleeDeviceId) {
        return new SipMessage(Constants.COMMAND_CONNECT + callerDeviceId + calleeDeviceId + "3");
    }

    public static SipMessage getControlMessage(String deviceId) {
        return new SipMessage(Constants.COMMAND_STOP_CONTROL + deviceId);
    }
    
    public static void main(String[] args) {
        System.out.println(getDtmfMessage("999010002", 3));
    }
}
