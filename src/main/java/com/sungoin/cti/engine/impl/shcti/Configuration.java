/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.engine.impl.shcti;

import com.sungoin.cti.engine.exception.InitEngineFailException;
import java.io.IOException;
import java.util.Properties;

/**
 * 
 * <AUTHOR>
 * 2015-5-4
 */
public class Configuration {
	private static final Properties prop = new Properties();

	public static int LOCAL_MOBILE = 1;
	public static String ALLWAY_CALLER = null;
	
	private Configuration() {
	}
	
	static {
		try {
			prop.load(Configuration.class.getClassLoader().getResourceAsStream(Constants.PROP_PATH));
			
			LOCAL_MOBILE = Integer.valueOf(prop.getProperty("local_mobile"));
			ALLWAY_CALLER = prop.getProperty("allway_caller");
		} catch (IOException ex) {
			throw new InitEngineFailException("can not load xms.properties!", ex);
		}
	}
	
	public static String getProperty(String key) {
		return prop.getProperty(key);
	}
	
	public static String getProperty(String key, String defaultValue) {
		return prop.getProperty(key, defaultValue);
	}
}
