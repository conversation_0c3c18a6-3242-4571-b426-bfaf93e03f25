/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.djkeygoe.local;

import DJKeygoe.DeviceID_t;

/**
 *
 * <AUTHOR> 2015-5-4 中继通道设备
 */
public class TRUNK_STRUCT extends BASE_STRUCT {

	public DeviceID_t deviceID = new DeviceID_t();
	public int iModSeqID = 0;
	public volatile int iLineState = 0;

	public DeviceID_t VocDevID = new DeviceID_t();
	public byte u8PlayTag = 0;

	// -----------------
	public int State = -1;

	public byte CallerCode[] = new byte[32];
	public byte CalleeCode[] = new byte[32];

	public String caller;
	public String callee;

	//会议设备
	public DeviceID_t ConfDevID = new DeviceID_t();
	public int confMode = 0;
	public boolean isConfRecord = false;
	public boolean isHost = false;

	public volatile int lsh = 0;
	public int callLsh = 0;
	public long offHookTime;
	public volatile boolean occupyed = false;
	public String relativeCallId;
    public volatile int callOutLsh = 0;

	public void InitCallCode() {
		for (int i = 0; i < 32; i++) {
			CallerCode[i] = 0;
			CalleeCode[i] = 0;
		}
		caller = null;
		callee = null;
	}

	@Override
	public String toString() {
		return "TRUNK_STRUCT{" + "uuid=" + uuid + ", lsh=" + lsh + '}';
	}

	public void init() {
		this.InitCallCode();
		this.relativeCallId = null;
		this.VocDevID = null;
		this.ConfDevID = null;
		this.offHookTime = 0;
		this.lsh = 0;
		this.callLsh = 0;
		this.confMode = 0;
		this.isHost = false;
		this.isConfRecord = false;
		this.occupyed = false;
        this.callOutLsh = 0;
	}
}
