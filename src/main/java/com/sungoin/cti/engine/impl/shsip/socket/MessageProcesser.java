/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip.socket;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.CTIEventProcess;
import com.sungoin.cti.engine.EngineState;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;
import com.sungoin.cti.engine.impl.shsip.Configuration;
import com.sungoin.cti.engine.impl.shsip.Constants;
import com.sungoin.cti.engine.impl.shsip.ShSipEvent;
import com.sungoin.cti.engine.impl.shsip.ShsipEngine;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class MessageProcesser implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(MessageProcesser.class);
    private static final String CALLEE_PREFIX = Configuration.getCalleePrefix();
    private static final BlockingQueue<String> QUEUE = new ArrayBlockingQueue<>(256);

    private final CTIEngine engine;

    public MessageProcesser(CTIEngine engine) {
        this.engine = engine;
    }

    public void process(String msg) {
        if (engine.getState() == EngineState.RUNNING) {
            boolean result = QUEUE.offer(msg);
            if (!result) {
                LOG.error(" MessageQueue is full !");
            }
        }
    }

    public void start() {
        new Thread(this).start();
    }

    @Override
    public void run() {
        LOG.info("MessageProcesser start running...");
        while (engine.getState() == EngineState.RUNNING) {
            try {
                String message = QUEUE.take();
                //"14  0082y163 0010"
                int length = Integer.valueOf(message.substring(0, 4).trim());
                if (message.length() != length + 3) {
                    LOG.warn("消息：{} 格式不正确，消息长度：{}，实际长度：{}", message, message.length(), length + 3);
                    continue;
                }
                String code = message.substring(4, 8);
                char command = message.charAt(8);
                String content = message.substring(9);
                switch (command) {
                    case Constants.COMMAND_REGISTER_ACK:
                    case Constants.COMMAND_LOGOUT_ACK:
                    case Constants.COMMAND_OUTLINE_CHANGE_ACK:
                    case Constants.COMMAND_STOP_CONTROL_ACK:    
                        processMessage(code, command, content);
                        break;
                    default:
                        processEvent(code, command, content);
                        break;
                }
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            }
        }
    }

    private void processEvent(String code, char command, String content) {
        EventSource source = null;
		EventType type = null;
		EventData edata = null;
        String deviceId = content.substring(0, 9);
        int lsh, state, stopCode, errorCode, reason;
        
        switch (command) {
            case Constants.COMMAND_CALLIN:
                LOG.debug("处理呼入事件：{}", content);
                lsh = ShsipEngine.getNextCallinLsh();
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                ((ShsipEngine) this.engine).putDeviceLsh(deviceId, lsh);
                edata = new EventData(deviceId, lsh, null);
                String caller = content.substring(9, content.indexOf(" "));
                String callee = content.substring(content.indexOf(" ") + 1, content.lastIndexOf(" "));
                callee = StringUtils.removeStart(callee, CALLEE_PREFIX);
				source = EventSource.TRUNK;
				type = EventType.CALLIN;
				edata.setCaller(caller);
				edata.setCallee(callee);
				edata.setModule("0");
				edata.setChannel("0");
                break;
            case Constants.COMMAND_ANSWER_ACK:
                LOG.debug("处理摘机事件：{}", content);
                lsh = ((ShsipEngine) this.engine).getDeviceLsh(deviceId);
                state = Integer.valueOf(content.substring(9));
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                edata = new EventData(deviceId, lsh, null);
				source = EventSource.TRUNK;
				type = EventType.ANSWERCALL;
				edata.setState(state == 0 ? 1 : 0);
				edata.setErrorCode(0);
                break;
            case Constants.COMMAND_PLAYEND_ACK:
                LOG.debug("处理放音结束事件：{}", content);
                lsh = ((ShsipEngine) this.engine).getDeviceLsh(deviceId);
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                //0082a99901000028
                if(lsh < 0) {
                    return;
                }
                edata = new EventData(deviceId, lsh, null);
				source = EventSource.VOC;
				type = EventType.PLAY;
                reason = Integer.valueOf(content.substring(9, 10));
				edata.setState(reason == 0 ? 1:0);
				edata.setStopCode(reason);
				edata.setErrorCode(0);
                break;
            case Constants.COMMAND_RECORDEND_ACK:
                LOG.debug("处理录音结束事件：{}", content);
                lsh = ((ShsipEngine) this.engine).getDeviceLsh(deviceId);
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                if(lsh < 0) {
                    return;
                }
                edata = new EventData(deviceId, lsh, null);
				source = EventSource.VOC;
				type = EventType.RECORD;
                reason = Integer.valueOf(content.substring(9, 10));
				edata.setState(reason == 0 ? 1:0);
				edata.setStopCode(reason);
				edata.setErrorCode(0);
                break;
            case Constants.COMMAND_DTMF_ACK:
                LOG.debug("处理收码事件：{}", content);
                lsh = ((ShsipEngine) this.engine).getDeviceLsh(deviceId);
                if(lsh < 0) {
                    return;
                }
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                //0082c999010000289
                reason = Integer.valueOf(content.substring(9, 10));
                source = EventSource.VOC;
                if(reason == 0 || reason == 2) {
                    edata = new EventData(deviceId, lsh, null);
                    type = EventType.DTMF;
                    String dtmf = content.substring(content.length() -1);
                    edata.setState(1);
                    edata.setDtmf(dtmf);
                }
                break;
            case Constants.COMMAND_CALLOUT_ACK:
                //"0082j9990100000010"
                LOG.debug("处理外呼结束事件：{}", content);
                lsh = Integer.valueOf(content.substring(9, 12));
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                state = Integer.valueOf(content.substring(12));
                source = EventSource.TRUNK;
                type = EventType.CALLOUT;
                if(state == 0) {
                    //呼叫成功，记录真正的设备ID
                    ((ShsipEngine) this.engine).putOutcallDevice(lsh, deviceId);
                }
                edata = new EventData(ShsipEngine.EMPTY_DEVICE_ID, lsh, null);
                edata.setState(state == 0 ? 1 : 0);
				edata.setErrorCode(state);
                break;
            case Constants.COMMAND_ONHOOK_ACK:
                LOG.debug("处理挂机事件：{}", content);
                state = Integer.valueOf(content.substring(9));
                if(state != 0) {
                    return;
                }
                if(deviceId.equals(ShsipEngine.EMPTY_DEVICE_ID)) {
                    lsh = Integer.valueOf(content.substring(9, 12));
                } else {
                    lsh = ((ShsipEngine) this.engine).getDeviceLsh(deviceId);
                }
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                if(lsh < 1000) {
                    //外呼挂机
                    edata = new EventData(ShsipEngine.EMPTY_DEVICE_ID, lsh, null);
                } else {
                    //呼入挂机
                    edata = new EventData(deviceId, lsh, null);
                }
				source = EventSource.TRUNK;
				type = EventType.CLEARCALL;
				edata.setState(state == 0 ? 1 : 0);
				edata.setErrorCode(0);
                ((ShsipEngine) this.engine).clearCall(deviceId);
                break;
            default:
                LOG.warn("未知事件类型：{},忽略此事件：{}", command, content);
                return;
        }
        if(edata != null) {
            ((CTIEventProcess) engine).processEvent(new ShSipEvent(source, type, edata));
        }
        if(source == EventSource.VOC) {
            processVocEvent(command, content, deviceId, lsh, source);
        }
    }

    private void processVocEvent(char command, String content, String deviceId, int lsh, EventSource source) {
        //特殊处理dtmf
        int reason = Integer.valueOf(content.substring(9, 10));
        switch (command) {
            case Constants.COMMAND_PLAYEND_ACK:
            {
                if(reason == 0 || reason == 2) {
                    //再发一个收码命令
                    ((ShsipEngine) engine).receiveDtmf(deviceId);
                }
                break;
            }
            case Constants.COMMAND_DTMF_ACK:
            {
                if(reason ==0 || reason == 2) {
                     //再发一个收码命令
                    ((ShsipEngine) engine).receiveDtmf(deviceId);
                } else if(reason == 1) {
                    //终止收码
                    ((ShsipEngine) this.engine).stopReceiveDtmf(deviceId);
                }
                break;
            }
//            case Constants.COMMAND_RECORDEND_ACK:
//            {
//
//            }
            default:
                break;
        }
        if(reason == 3) {
            ((ShsipEngine) engine).onHookInternal(deviceId);
        }
    }
    
    private void processMessage(String code, char command, String content) {
        String deviceId;
        switch (command) {
            case Constants.COMMAND_REGISTER_ACK:
                LOG.debug("处理注册确认消息：{}", content);
                char flag = content.charAt(content.length() - 1);
                ((ShsipEngine) this.engine).putAckMessage(code + Constants.COMMAND_REGISTER_ACK + ShsipEngine.MASTER_ID , flag == '0');
                break;
            case Constants.COMMAND_LOGOUT_ACK:
                LOG.debug("处理注销确认消息：{}", content);
                if(((ShsipEngine) this.engine).isRegisterSuccess()) {
                    ((ShsipEngine) this.engine).register();
                }
                break;
            case Constants.COMMAND_OUTLINE_CHANGE_ACK:
                LOG.debug("处理外线状态变化消息：{}", content);
                deviceId = content.substring(0, 9);
                int state = Integer.valueOf(content.substring(9));
                if(state == 0) {
                    //发送挂机指令
                    ((ShsipEngine) this.engine).onHookInternal(deviceId);
                }
//            case Constants.COMMAND_STOP_CONTROL_ACK:
//                LOG.debug("处理语音通道停止消息：{}", content);
//                deviceId = content.substring(0, 9);
//               ((ShsipEngine) this.engine).controlStopped(deviceId);
//                break;
            default:
                LOG.warn("未知消息类型：{},忽略此消息：{}", command, content);
                break;
        }
    }
}
