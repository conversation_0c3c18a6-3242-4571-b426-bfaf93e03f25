/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip.socket;

import com.sungoin.cti.engine.impl.shsip.ShsipEngine;
import org.apache.commons.lang3.StringUtils;
import org.apache.mina.core.buffer.IoBuffer;

/**
 *
 * <AUTHOR>
 */
public class SipMessage {

    //结束符
    public static final byte END_FLAG = 0x0a;
    public static final String SPACE = " ";

    private final int length;
    private final String content;

    public SipMessage(String content) {
        this.length = content.length() + 5;
        this.content = String.format("%-4s", length) + ShsipEngine.LOCAL_CODE + content;
    }

    public int getLength() {
        return length;
    }

    public String getContent() {
        return content;
    }

    public IoBuffer getIoBuffer() {
        IoBuffer buffer = IoBuffer.allocate(content.length() + 1).setAutoExpand(true);
        buffer.put(this.content.getBytes());
        buffer.put(SipMessage.END_FLAG);
        buffer.flip();
        return buffer;
    }

    @Override
    public String toString() {
        return "SipMessage{" + "length=" + length + ", content=" + content + "}";
    }


    public static void main(String[] args) {
        String s = "008651380108029";
        s = StringUtils.removeStart(s, "0086513");
        System.out.println(s);
    }
}
