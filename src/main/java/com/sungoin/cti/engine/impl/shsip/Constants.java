/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip;

/**
 *
 * <AUTHOR>
 */
public class Constants {
    public static final int STATE_SUCCESS = 1;

	public static final String PROP_PATH = "shsip/shsip.properties";
	public static final String SHSIP_SERVER_IP = "shsip.server.ip";
	public static final String SHSIP_SERVER_PORT = "shsip.server.port";
    public static final String LOCAL_CODE = "shsip.local.code";
    public static final String MASTER_ID = "shsip.local.masterId";
    public static final String MACHINE_NO = "shsip.local.machineNo";
    public static final String HEART_BEAT_SECONDS = "shsip.heartbeat.seconds";
    public static final String CALLEE_PREFIX = "shsip.callee.prefix";
    public static final String TEST_CALLER = "shsip.test.caller";
    
    public static final char COMMAND_REGISTER = 'Y';
    public static final char COMMAND_REGISTER_ACK = 'y';
    public static final char COMMAND_LOGOUT = 'Z';
    public static final char COMMAND_LOGOUT_ACK = 'z';
    public static final char COMMAND_HEARTBEAT = '?';
    
    public static final char COMMAND_CALLIN = 'h';
    public static final char COMMAND_ANSWER = 'I';
    public static final char COMMAND_ANSWER_ACK = 'i';
    public static final char COMMAND_PLAY = 'A';
    public static final char COMMAND_PLAYEND_ACK = 'a';
    public static final char COMMAND_RECORD = 'B';
    public static final char COMMAND_RECORDEND_ACK = 'b';
    public static final char COMMAND_STOP_CONTROL = 'E';
    public static final char COMMAND_STOP_CONTROL_ACK = 'e';
    public static final char COMMAND_CALLOUT = 'J';
    public static final char COMMAND_CALLOUT_ACK = 'j';
    public static final char COMMAND_DTMF_ACK = 'c';
    public static final char COMMAND_ONHOOK = 'L';
    public static final char COMMAND_ONHOOK_ACK = 'l';
    public static final char COMMAND_OUTLINE_CHANGE_ACK = 'k';
    public static final char COMMAND_DTMF = 'C';
    public static final char COMMAND_CONNECT = 'M';
    public static final char COMMAND_CONNECT_ACK = 'm';
}
