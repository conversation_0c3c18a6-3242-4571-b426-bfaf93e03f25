package com.sungoin.cti.engine.impl.xcc.nats;

import io.nats.client.Connection;
import io.nats.client.Message;
import org.json.simple.JSONObject;

import java.nio.charset.StandardCharsets;
import java.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Request {
    static Logger log = LoggerFactory.getLogger(Request.class);
    
    public void NatsRequest(String id, String service, String method, JSONObject body, Connection con) {
        JSONObject rpc = new JSONObject();
        rpc.put("jsonrpc", "2.0");
        rpc.put("method", method);
        rpc.put("id", id);
        rpc.put("params", body);
        String request = rpc.toJSONString();
        log.debug("publish Nats 消息：{}", request);
        con.publish(service, request.getBytes(StandardCharsets.UTF_8));

    }

    public Message NatsRequestTimeOut(String id, String service, String method, JSONObject body, Connection con, Duration duration) {
        JSONObject rpc = new JSONObject();
        rpc.put("jsonrpc", "2.0");
        rpc.put("method", method);
        rpc.put("id", id);
        rpc.put("params", body);
        String request = rpc.toJSONString();
        log.debug("request Nats 消息：{}", request);
        Message message = null;
        try {
            message = con.request(service, request.getBytes(StandardCharsets.UTF_8), duration);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return message;
    }
    
    public static void main(String[] args) {
        String s = "/usr/local/freeswitch/storage/recordings/2023-07/auto-record-20230731-133743-02131233355-80108029-7b7e1173-b6ff-4164-8f8f-7953529a0a0d.wav";
        System.out.println(s.replace("/freeswitch/storage", "/xswitch-6.0.7/data"));
    }
}
