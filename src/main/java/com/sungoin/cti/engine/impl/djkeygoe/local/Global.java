package com.sungoin.cti.engine.impl.djkeygoe.local;

import DJKeygoe.DJAcsAPIDef;
import com.sungoin.cti.engine.impl.djkeygoe.Constants;
import DJKeygoe.*;
import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;
import com.sungoin.cti.engine.LineState;
import com.sungoin.cti.engine.exception.ConferenceFullException;
import com.sungoin.cti.engine.exception.IllegalFilePathExcaption;
import com.sungoin.cti.engine.exception.InitEngineFailException;
import com.sungoin.cti.engine.exception.TrunkDeviceFullException;
import com.sungoin.cti.engine.exception.VoiceDeviceFullException;
import com.sungoin.cti.server.scoket.CtiState;
import com.sungoin.cti.server.service.asr.AsrService;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Global {
	public static final Logger log = LoggerFactory.getLogger(Global.class);
	private static final AtomicInteger LSH = new AtomicInteger(1);
	private static final AtomicInteger CALL_LSH = new AtomicInteger(1);

	public static int getNextLsh() {
		return LSH.getAndIncrement();
	}

	public static int getNextCallLsh() {
		return CALL_LSH.getAndIncrement();
	}
	public static final int MAX_DSP_MODULE_NUMBER_OF_XMS = Integer.parseInt(Configuration.getProperty("MAX_DSP_MODULE_NUMBER_OF_XMS"));
	public static final int MAX_VOC_RES_NUM_EACH_DSP = Integer.parseInt(Configuration.getProperty("MAX_VOC_RES_NUM_EACH_DSP"));
	public static final int MAX_PCM_RES_NUM_EACH_DSP = Integer.parseInt(Configuration.getProperty("MAX_PCM_RES_NUM_EACH_DSP"));
	public static final int MAX_TRUNK_RES_NUM_EACH_DSP = Integer.parseInt(Configuration.getProperty("MAX_TRUNK_RES_NUM_EACH_DSP"));
	public static final int MAX_CONF_RES_NUM_EACH_DSP = Integer.parseInt(Configuration.getProperty("MAX_CONF_RES_NUM_EACH_DSP"));
	
	private final DJAcsAPIDef api = DJAcsAPIDef.getInstance();
	public long licenseQueryTime = 0;
	public int licenseDays = 0;

	TYPE_XMS_DSP_DEVICE_RES[] AllDeviceRes = new TYPE_XMS_DSP_DEVICE_RES[MAX_DSP_MODULE_NUMBER_OF_XMS];

	//DSP设备
	public AtomicInteger g_iTotalModule = new AtomicInteger(0);
	public byte MapTable_Module[] = new byte[MAX_DSP_MODULE_NUMBER_OF_XMS];

	//PCM设备
	public AtomicInteger g_iTotalPcm = new AtomicInteger(0);
	public AtomicInteger g_iTotalPcmOpened = new AtomicInteger(0);
	TYPE_CHANNEL_MAP_TABLE[] MapTable_Pcm = new TYPE_CHANNEL_MAP_TABLE[MAX_DSP_MODULE_NUMBER_OF_XMS * MAX_PCM_RES_NUM_EACH_DSP];

	//通道设备
	public AtomicInteger g_iTotalTrunk = new AtomicInteger(0);
	public AtomicInteger g_iTotalTrunkOpened = new AtomicInteger(0);
	TYPE_CHANNEL_MAP_TABLE[] MapTable_Trunk = new TYPE_CHANNEL_MAP_TABLE[MAX_DSP_MODULE_NUMBER_OF_XMS * MAX_TRUNK_RES_NUM_EACH_DSP];

	//语音设备
	public AtomicInteger g_iTotalVoice = new AtomicInteger(0);
	public AtomicInteger g_iTotalVoiceOpened = new AtomicInteger(0);
	public AtomicInteger g_iTotalVoiceFree = new AtomicInteger(0);
	TYPE_CHANNEL_MAP_TABLE[] MapTable_Voice = new TYPE_CHANNEL_MAP_TABLE[MAX_DSP_MODULE_NUMBER_OF_XMS * MAX_VOC_RES_NUM_EACH_DSP];

	//会议设备
	public AtomicInteger g_iTotalConf = new AtomicInteger(0);
	public AtomicInteger g_iTotalConfOpened = new AtomicInteger(0);
	public AtomicInteger g_iTotalConfFree = new AtomicInteger(0);
	TYPE_CHANNEL_MAP_TABLE[] MapTable_Conf = new TYPE_CHANNEL_MAP_TABLE[MAX_DSP_MODULE_NUMBER_OF_XMS * MAX_CONF_RES_NUM_EACH_DSP];

	public String cfg_CallingNum = "";
	public String cfg_SimCalledNum = "";
	public ServerID_t cfg_ServerID = new ServerID_t();

	//是否指定应用模块 0 否 1是
	public int cfg_iPartWork = 0;
	//DSP 模块序号
	public int cfg_iPartWorkModuleID = 0;
	public int cfg_s32DebugOn = 0;
	public int cfg_iVoiceRule = 0;		// search voice in Fix Relationship
	public int g_iLoopStart = 0;
	TYPE_ANALOG_GTD_PARAM g_Param_Analog = new TYPE_ANALOG_GTD_PARAM();

	CmdParamData_GtdFreq_t g_TmpGtdFreq = new CmdParamData_GtdFreq_t();
	CmdParamData_GtdProtoType_t g_TmpGtdProto = new CmdParamData_GtdProtoType_t();
	CmdParamData_AnalogTrunk_t g_AnalogTrk = new CmdParamData_AnalogTrunk_t();
	CmdParamData_Voice_t g_CmdVoc = new CmdParamData_Voice_t();
	CmdParamData_CAS_t g_Param_CAS = new CmdParamData_CAS_t();

	public int g_acsHandle = -1;
    public int g_acsCspHandler = -1;
	private final byte g_8AppID = 1;

	private final CTIEngine engine;

	//add by chenlei for multithread
	private final Lock trunkLock = new ReentrantLock();
	private final Lock vocLock = new ReentrantLock();
	private final Lock confLock = new ReentrantLock();

	private final Map<String, TRUNK_STRUCT> ALL_TRUNK_MAP = new HashMap<String, TRUNK_STRUCT>();
//	private final ScheduledExecutorService ses = Executors.newSingleThreadScheduledExecutor();
	
	private byte dsp_voc_order[] = new byte[MAX_DSP_MODULE_NUMBER_OF_XMS];
    
    //记录外呼id的map
    final Map<String, String[]> CALL_OUT_MAP =  new ConcurrentHashMap<>();
    
    //asr服务类
//    AsrService asrService = new AsrService();
	
	void initTrunkToMap(TRUNK_STRUCT oneTrk) {
		this.ALL_TRUNK_MAP.put(oneTrk.uuid, oneTrk);
	}

	public Global(CTIEngine engine) {
		this.engine = engine;
		this.InitCmdParamData_CASData();
		this.InitGlobalObjectArr();
		this.ReadFromConfig();
	}

	public void start() {
		int ret = this.openStream();
		log.info("DJKeygoe openStream return : {}", ret);
		if (ret < 1) {
			throw new InitEngineFailException("DJkeygoe openStream fail...");
		}
//		startSchedule();
//        ret = this.openExtStream();
//        log.info("DJKeygoe openExtStream return : {}", ret);
//        if (ret < 1) {
//			throw new InitEngineFailException("DJkeygoe openExtStream fail...");
//		}
		log.info("ISUP_SP_BackwardCallIndicator.m_u8EndToEndInformationIndicator = {}", Configuration.ISUP_SP_BackwardCallIndicator_m_u8EndToEndInformationIndicator);
	}

//	private void startSchedule() {
//		long oneDay = 24 * 60 * 60 * 1000;
//		long period = 2 * 60 * 60 * 1000;
//		Calendar calendar = Calendar.getInstance();
//		calendar.set(Calendar.HOUR_OF_DAY, 4);
//		calendar.set(Calendar.MINUTE, 0);
//		calendar.set(Calendar.SECOND, 0);
//		long initDelay = calendar.getTimeInMillis() - System.currentTimeMillis();
//		if (initDelay < 0) {
//			initDelay = oneDay + initDelay;
//		}
//		ses.scheduleAtFixedRate(new Runnable() {
//			@Override
//			public void run() {
//				checkVocDevice();
//			}
//		}, initDelay, period, TimeUnit.MILLISECONDS);
//	}

	public void init() {
		int ret = this.setESR();
		log.info("DJKeygoe setESR return : {}", ret);
		if (ret < 1) {
			throw new InitEngineFailException("DJkeygoe setESR fail...");
		}
		ret = this.getDeviceList();
		log.info("DJKeygoe getDeviceList return : {}", ret);
		if (ret < 1) {
			throw new InitEngineFailException("DJkeygoe getDeviceList fail...");
		}
//		try {
//			Thread.sleep(300);
//			for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
//				if (m.lFlag != 0) {
//					api.XMS_acsQueryLicense(this.g_acsHandle, m.deviceID, null);
//					break;
//				}
//			}
//		} catch (Exception ex) {
//			log.error(ex.getMessage());
//		}
	}

	public void shutdown() {
		int ret = this.closeStream();
		log.info("DJKeygoe closeStream return : {}", ret);
	}

	//add by chenlei
	private int openStream() {
		PrivateData_t priData = new PrivateData_t();
		priData.m_u32DataSize = 0;
		this.g_acsHandle = api.XMS_acsOpenStream(this.g_acsHandle,
			this.cfg_ServerID, g_8AppID, Configuration.SEND_Q_SIZE, Configuration.RECV_Q_SIZE, this.cfg_s32DebugOn, priData);
		return this.g_acsHandle;
	}
    
    private int openExtStream() {
		ServerID_t server = new ServerID_t();
		CharsToByte(server.m_s8ServerIp, "***************".toCharArray(), "***************".length());
		server.m_u32ServerPort = 6000;
		return api.XMS_acsOpenStreamExt(this.g_acsHandle, server, null);
	}

	private int openDevice(DeviceID_t deviceID) {
		PrivateData_t priData = new PrivateData_t();
		priData.m_u32DataSize = 0;
		return api.XMS_ctsOpenDevice(g_acsHandle, deviceID, priData);
	}

	public boolean isLshUsed(int callLsh) {
		for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
			if (m.lFlag != 0) {
				for (TRUNK_STRUCT t : m.pTrunk) {
					if (t.callLsh != 0 && t.callLsh == callLsh) {
						return true;
					}
				}
			}
		}
		return false;
	}

	/**
	 *
	 * @param deviceID deviceID = Trunk_Struct.uuid + "_" + Trunk_Struct.lsh
	 * @return
	 */
	public TRUNK_STRUCT getTrunkStructByID(String deviceID) {
		String[] tmp = deviceID.split("_");
		String id = tmp[0];
		int lsh = Integer.parseInt(tmp[1]);
		TRUNK_STRUCT oneTrk = ALL_TRUNK_MAP.get(id);
		if (lsh == 0 || oneTrk.lsh == lsh) {
			return oneTrk;
		} else {
			log.warn("设备的lsh发生改变！传入的流水号：{},实际的流水号：{}", lsh, oneTrk.lsh);
            //老的流水号发一个挂机事件
            EventData event = new EventData(oneTrk.uuid, lsh, null);
            event.setState(1);
            engine.processEvent(new DJKeygoeEvent(EventSource.TRUNK, EventType.CLEARCALL, event));
			throw new IllegalArgumentException("根据设备ID和流水号：" + deviceID + ",找不到对应设备！");
		}
	}

	public CONF_STRUCT getConfStructByID(String deviceID) {
		for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
			if (m.lFlag != 0) {
				for (CONF_STRUCT t : m.pConf) {
					if (t.uuid != null && t.uuid.equals(deviceID)) {
						return t;
					}
				}
			}
		}
		return null;
	}

	public int alertCallin(String deviceID) throws VoiceDeviceFullException {
		TRUNK_STRUCT OneTrk = getTrunkStructByID(deviceID);
		if (OneTrk.lsh != 0 && OneTrk.VocDevID == null) {
			bindFreeVocToOneTrunk(OneTrk);
		}
        if(Configuration.CALL_INDICATOR) {
            int ret = this.setOptBackwardCallIndicator(OneTrk.deviceID);
            log.debug("设置任选后向呼叫指示码返回：{}", ret);
        }
        if(Configuration.ISUP_SP_BackwardCallIndicator_m_u8EndToEndInformationIndicator != 0) {
            int ret = this.setBackwardCallIndicator(OneTrk.deviceID, (byte)Configuration.ISUP_SP_BackwardCallIndicator_m_u8EndToEndInformationIndicator);
            log.debug("设置后向呼叫指示码返回：{}", ret);
        }
		return api.XMS_ctsAlertCall(g_acsHandle, OneTrk.deviceID, null);
	}

	private void bindVocToTrunk(TRUNK_STRUCT oneTrk, DeviceID_t voc) {
		oneTrk.u8PlayTag = 16;
		oneTrk.VocDevID = voc;
		VOICE_STRUCT OneVoc = GetVoiceStructByDevice(voc.m_s8ModuleID, voc.m_s16ChannelID);
		if(OneVoc.UsedDevID != null && OneVoc.UsedDevID.m_s8ModuleID != 0) {
			My_DualUnlink(OneVoc.UsedDevID, voc);
			OneVoc.UsedDevID = null;
		}
		OneVoc.UsedDevID = oneTrk.deviceID;
		OneVoc.callDeviceId = oneTrk.uuid;
		OneVoc.callLsh = oneTrk.lsh;
		My_DualLink(oneTrk.deviceID, voc);
		
		if (oneTrk.deviceID.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH
			&& oneTrk.deviceID.m_s16DeviceSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_ANALOG_TRUNK) {
			SetGtD_AnalogTrunk(voc);		// prepare for get Busy Tone
		}
		log.debug("{}绑定的语音设备：{}", oneTrk.uuid, getDeviceInfo(voc));
	}

	private void bindFreeVocToOneTrunk(TRUNK_STRUCT oneTrk) throws VoiceDeviceFullException {
		DeviceID_t FreeVocDeviceID = SearchOneFreeVoice(oneTrk);
        if(FreeVocDeviceID == null) {
            checkVocDevice();
            FreeVocDeviceID = SearchOneFreeVoice(oneTrk);
        }
        if(FreeVocDeviceID == null) {
            throw new VoiceDeviceFullException("找不到空闲语音通道！");
        }
		bindVocToTrunk(oneTrk, FreeVocDeviceID);
	}

	private void makeCallSuccess(TRUNK_STRUCT oneTrk) {
		try {
			bindFreeVocToOneTrunk(oneTrk);
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
	}

	public int answerCallin(String deviceID) throws VoiceDeviceFullException {
		TRUNK_STRUCT OneTrk = getTrunkStructByID(deviceID);
		if (OneTrk.lsh != 0 && OneTrk.VocDevID == null) {
			bindFreeVocToOneTrunk(OneTrk);
		}
		return api.XMS_ctsAnswerCallIn(g_acsHandle, OneTrk.deviceID, null);
	}

	public int onHook(String deviceId) {
		DeviceID_t dev = this.getTrunkStructByID(deviceId).deviceID;
		return this.clearCall(dev);
	}

	private int clearCall(DeviceID_t deviceID) {
		return api.XMS_ctsClearCall(g_acsHandle, deviceID, 0, null);
	}

	private int closeStream() {
		return api.XMS_acsCloseStream(this.g_acsHandle, null);
	}

	private int setESR() {
		EsrFunc esr = new EsrFuncImpl(this);
		return api.XMS_acsSetESR(this.g_acsHandle, esr, 0, 1);
	}

	private int getDeviceList() {
		PrivateData_t priData = new PrivateData_t();
		priData.m_u32DataSize = 0;
		return api.XMS_acsGetDeviceList(this.g_acsHandle, priData);
	}

	public String[] makeCallinLine(String caller, String callee) throws TrunkDeviceFullException, VoiceDeviceFullException {
		TYPE_XMS_DSP_DEVICE_RES b = null;
		for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
			if (m.lFlag != 0) {
				b = m;
				break;
			}
		}
		short s16ChannelID = (short) Integer.parseInt(callee);
		if (b == null || b.deviceID == null) {
			throw new NullPointerException("TYPE_XMS_DSP_DEVICE_RES is null !");
		}
		log.debug("make call inline... dev.moduleId:{},dev.channelId:{}", b.deviceID.m_s8ModuleID, s16ChannelID);
		TRUNK_STRUCT trunk = GetTrunkStructByDevice(b.deviceID.m_s8ModuleID, s16ChannelID);
		if (trunk.State != TRUNK_STATE.TRK_FREE) {
			throw new TrunkDeviceFullException("trunk device is not free channel:" + callee);
		}
		DeviceID_t freeVocDevice = SearchOneFreeVoice(trunk);
        if(freeVocDevice == null) {
            checkVocDevice();
            freeVocDevice = SearchOneFreeVoice(trunk);
        }
        if(freeVocDevice == null) {
            throw new VoiceDeviceFullException("找不到空闲语音通道！");
        }
		trunk.VocDevID = freeVocDevice;
		VOICE_STRUCT oneVoc = GetVoiceStructByDevice(freeVocDevice.m_s8ModuleID, freeVocDevice.m_s16ChannelID);
		if(oneVoc.UsedDevID != null && oneVoc.UsedDevID.m_s8ModuleID != 0) {
			My_DualUnlink(oneVoc.UsedDevID, freeVocDevice);
			oneVoc.UsedDevID = null;
		}
		oneVoc.UsedDevID = trunk.deviceID;
		My_DualLink(trunk.deviceID, trunk.VocDevID);
		if (trunk.deviceID.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH
			&& trunk.deviceID.m_s16DeviceSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_ANALOG_TRUNK) {
			SetGtD_AnalogTrunk(freeVocDevice);		// prepare for get Busy Tone
		}
		byte s8Calling[] = new byte[caller.length() + 1];
		byte s8Called[] = new byte[callee.length() + 1];
		CharsToByte(s8Calling, caller.toCharArray(), caller.length());
		CharsToByte(s8Called, callee.toCharArray(), callee.length());
		//返回 1：成功；-1：失败
		int ret = api.XMS_ctsMakeCallOut(g_acsHandle, trunk.deviceID, s8Calling, s8Called, null);
		if (ret > 0) {
			trunk.lsh = getNextLsh();
			trunk.callLsh = getNextCallLsh();
			return new String[]{trunk.lsh + "", trunk.uuid, trunk.deviceID.m_s8ModuleID + "", trunk.deviceID.m_s16ChannelID + ""};
		} else {
			log.warn("makeCallinLine fail ...");
			return null;
		}
	}

	public String[] makeCalloutLine(String caller, String callee) throws TrunkDeviceFullException, VoiceDeviceFullException {
		return makeCalloutLine(caller, callee, null, null);
	}

	public String[] makeCalloutLine(String caller, String callee, String origCallee, String redirectNo) throws TrunkDeviceFullException, VoiceDeviceFullException {
		TRUNK_STRUCT OneTrk = SearchOneFreeTrunk();
		log.debug("找到外呼用的空闲通道：{}", OneTrk);
		DeviceID_t FreeTrkDeviceID = OneTrk.deviceID;
		if (FreeTrkDeviceID.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH
			&& FreeTrkDeviceID.m_s16DeviceSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_ANALOG_TRUNK) {
			DeviceID_t FreeVocDeviceID = SearchOneFreeVoice(OneTrk);
            if(FreeVocDeviceID == null) {
                checkVocDevice();
                FreeVocDeviceID = SearchOneFreeVoice(OneTrk);
            }
            if(FreeVocDeviceID == null) {
                throw new VoiceDeviceFullException("找不到空闲语音通道！");
            }
			VOICE_STRUCT OneVoc = GetVoiceStructByDevice(FreeVocDeviceID.m_s8ModuleID, FreeVocDeviceID.m_s16ChannelID);
			if(OneVoc.UsedDevID != null) {
				My_DualUnlink(OneVoc.UsedDevID, FreeVocDeviceID);
				OneVoc.UsedDevID = null;
			}
			OneVoc.UsedDevID = FreeTrkDeviceID;
			My_DualLink(FreeTrkDeviceID, FreeVocDeviceID);
			OneTrk.VocDevID = FreeVocDeviceID;

			SetGtD_AnalogTrunk(FreeVocDeviceID);
		}

		byte s8Calling[] = new byte[caller.length()];
		byte s8Called[] = new byte[callee.length()];
		CharsToByte(s8Calling, caller.toCharArray(), caller.length());
		CharsToByte(s8Called, callee.toCharArray(), callee.length());
		//返回 1：成功；-1：失败
		int ret = 0;
		if (OneTrk.deviceID.m_s16DeviceSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_E1_SS7_ISUP) {
			ret = this.setCallerParam(OneTrk.deviceID, caller);
			log.info("设置主叫参数：{},返回：{}", caller, ret);
			ret = this.setCalledParam(OneTrk.deviceID, callee);
			log.info("设置被叫参数：{},返回：{}", callee, ret);
			if (origCallee != null) {
				log.info("使用呼叫转移方式外呼！");
				ret = this.setOrigCalledParam(OneTrk.deviceID, origCallee);
				log.info("设置原始被叫参数：{},返回：{}", origCallee, ret);
            }
            if(redirectNo != null) {
				ret = this.setRedirectionNoParam(OneTrk.deviceID, redirectNo);
				log.info("设置改发号码参数：{},返回：{}", origCallee, ret);
				ret = this.setRedirectionInfoParam(OneTrk.deviceID);
				log.info("设置改发信息参数,返回：{}", ret);
			}
		}
		ret = api.XMS_ctsMakeCallOut(g_acsHandle, OneTrk.deviceID, s8Calling, s8Called, null);
		if (ret > 0) {
			OneTrk.lsh = getNextLsh();
            OneTrk.callOutLsh = OneTrk.lsh;
			OneTrk.callLsh = getNextCallLsh();
            //记录外呼设备
            CALL_OUT_MAP.put(OneTrk.uuid, new String[] {caller, callee});
			return new String[]{OneTrk.lsh + "", OneTrk.uuid, OneTrk.deviceID.m_s8ModuleID + "", OneTrk.deviceID.m_s16ChannelID + ""};
		} else {
			log.warn("makeCalloutLine fail ...");
			return null;
		}
	}
    public int connectCall(String callerDeviceId, String calleeDeviceId) {
        return connectCall(callerDeviceId, calleeDeviceId, true);
    }
    
	public int connectCall(String callerDeviceId, String calleeDeviceId, boolean sameDsp) {
		TRUNK_STRUCT caller = null;
		TRUNK_STRUCT callee = null;
		try {
			caller = getTrunkStructByID(callerDeviceId);
			callee = getTrunkStructByID(calleeDeviceId);
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			return 0;
		}
		callee.callLsh = caller.callLsh;
		caller.relativeCallId = calleeDeviceId;
		callee.relativeCallId = callerDeviceId;
        if(callee.VocDevID == null) {
            //检查被叫是否分配到语音通道
            log.warn("被叫未分配到语音通道，根据主叫dsp重新分配语音通道");
            DeviceID_t free = this.SearchOneFreeVoiceByDspID(caller.VocDevID.m_s8ModuleID);
            if (free != null) {
				bindVocToTrunk(callee, free);
			} else {
                log.warn("找不到空闲的语音通道！");
				return -1;
            }
        }
        if(sameDsp) {
            if (caller.VocDevID.m_s8ModuleID != callee.VocDevID.m_s8ModuleID) {
                //检查2个通话的语音通道是否在同一个dsp上,如果不在，需要重新分配
                byte callerDsp = caller.VocDevID.m_s8ModuleID;
                byte calleeDsp = callee.VocDevID.m_s8ModuleID;
                log.warn("主被叫的语音设备不在同一个dsp上，caller:{}, callee:{}，重新分配！", callerDsp, calleeDsp);
                DeviceID_t free = this.SearchOneFreeVoiceByDspID(callerDsp);
                if (free != null) {
                    log.info("根据主叫的语音通道的dsp：{}找到空闲语音通道", callerDsp);
                    //释放原来被叫的语音设备
                    freeVocDevice(callee.VocDevID);
                    bindVocToTrunk(callee, free);
                } else {
                    checkVocDevice();
                    free = this.SearchOneFreeVoiceByDspID(calleeDsp);
                    if (free != null) {
                        log.info("根据被叫的语音通道的dsp：{}找到空闲语音通道", calleeDsp);
                        freeVocDevice(caller.VocDevID);
                        bindVocToTrunk(caller, free);
                    } else {
                        log.warn("找不到2个相同dsp的语音通道！");
                        return -1;
                    }
                }
            } else {
                log.info("主被叫语音通道在同一个dsp上：{}", caller.VocDevID.m_s8ModuleID);
            }
        } else {
            log.info("不检查主被叫是否在同一个dsp上");
        }
//		if(caller.VocDevID != null) {
//			api.XMS_ctsLinkDevice(g_acsHandle, caller.deviceID, caller.VocDevID, null);
//		}
//		if(callee.VocDevID != null) {
//			api.XMS_ctsLinkDevice(g_acsHandle, callee.deviceID, callee.VocDevID, null);
//		}
		My_DualLink(caller.deviceID, callee.deviceID);
		return 1;
	}

	public int disConnectCall(String callerDeviceId, String calleeDeviceId) {
		TRUNK_STRUCT caller = null;
		TRUNK_STRUCT callee = null;
		try {
			caller = getTrunkStructByID(callerDeviceId);
			callee = getTrunkStructByID(calleeDeviceId);
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
			return 0;
		}
		if (caller.deviceID != null && callee.deviceID != null) {
			My_DualUnlink(caller.deviceID, callee.deviceID);
			caller.relativeCallId = null;
			callee.relativeCallId = null;
			//重新关联主被叫的设备ID和语音ID
			if (caller.VocDevID != null) {
				api.XMS_ctsLinkDevice(g_acsHandle, caller.VocDevID, caller.deviceID, null);
			}
			if (callee.VocDevID != null) {
				api.XMS_ctsLinkDevice(g_acsHandle, callee.VocDevID, callee.deviceID, null);
			}
		}
		return 1;
	}

	/**
	 * 设置改发号码
	 *
	 * @param device
	 * @param callerNo
	 * @return
	 */
	private int setRedirectionNoParam(DeviceID_t device, String callee) {
		ISUP_spRedirectingNumber reNumber = new ISUP_spRedirectingNumber();
		reNumber.m_u8NatureOfAddressIndicator = (byte) Configuration.ISUP_spRedirectingNumber_m_u8NatureOfAddressIndicator;
		reNumber.m_u8OddEvenIndicator = (byte) Configuration.ISUP_spRedirectingNumber_m_u8OddEvenIndicator;
		reNumber.m_u8AddressPresentationRestrictedIndicator = (byte) Configuration.ISUP_spOriginalCalledNumber_m_u8AddressPresentationRestrictedIndicator;
		reNumber.m_u8NumberingPlanIndicator = (byte) Configuration.ISUP_spOriginalCalledNumber_m_u8NumberingPlanIndicator;
		byte[] s8ReNo = callee.getBytes();
		for (int i = 0; i < s8ReNo.length && i < 40; i++) {
			reNumber.m_s8AddressSignal[i] = s8ReNo[i];
		}
		return api.XMS_ctsSetParam(g_acsHandle, device,
			(short) XMS_ISUP_Signalling_Parameter.ISUP_SP_RedirectingNumber, (short) 0, reNumber);
	}

	/**
	 * 设置改发信息
	 *
	 * @param device
	 * @param callerNo
	 * @return
	 */
	private int setRedirectionInfoParam(DeviceID_t device) {
		ISUP_spRedirectionInformation info = new ISUP_spRedirectionInformation();
		info.m_u8RedirectingIndicator = (byte) Configuration.ISUP_spRedirectionInformation_m_u8RedirectingIndicator;
		info.m_u8OriginalRedirectionReason = (byte) Configuration.ISUP_spRedirectionInformation_m_u8OriginalRedirectionReason;
		info.m_u8RedirectionCounter = (byte) Configuration.ISUP_spRedirectionInformation_m_u8RedirectionCounter;
		info.m_u8RedirectingReason = (byte) Configuration.ISUP_spRedirectionInformation_m_u8RedirectingReason;
		return api.XMS_ctsSetParam(g_acsHandle, device,
			(short) XMS_ISUP_Signalling_Parameter.ISUP_SP_RedirectionInformation, (short) 0, info);
	}

	/**
	 * 设置原始被叫
	 *
	 * @param device
	 * @param origCalled
	 * @return
	 */
	private int setOrigCalledParam(DeviceID_t device, String callee) {
		ISUP_spOriginalCalledNumber origCalled = new ISUP_spOriginalCalledNumber();
		origCalled.m_u8NatureOfAddressIndicator = (byte) Configuration.ISUP_spOriginalCalledNumber_m_u8NatureOfAddressIndicator;
		origCalled.m_u8OddEvenIndicator = (byte) Configuration.ISUP_spOriginalCalledNumber_m_u8OddEvenIndicator;
		origCalled.m_u8AddressPresentationRestrictedIndicator = (byte) Configuration.ISUP_spOriginalCalledNumber_m_u8AddressPresentationRestrictedIndicator;
		origCalled.m_u8NumberingPlanIndicator = (byte) Configuration.ISUP_spOriginalCalledNumber_m_u8NumberingPlanIndicator;
		byte[] s8OrigNo = callee.getBytes();
		for (int i = 0; i < s8OrigNo.length && i < 40; i++) {
			origCalled.m_s8AddressSignal[i] = s8OrigNo[i];
		}
		return api.XMS_ctsSetParam(g_acsHandle, device,
			(short) XMS_ISUP_Signalling_Parameter.ISUP_SP_OriginalCalledNumber, (short) 0, origCalled);
	}

    private int setOptBackwardCallIndicator(DeviceID_t device) {
        ISUP_spOptBackwardCallIndicator optCallIndicator = new ISUP_spOptBackwardCallIndicator();
        optCallIndicator.m_u8InBandInformationIndicator = 1;
        return api.XMS_ctsSetParam(g_acsHandle, device,
			(short) XMS_ISUP_Signalling_Parameter.ISUP_SP_OptBackwardCallIndicator, (short) 0, optCallIndicator);
    }
    
    private int setBackwardCallIndicator(DeviceID_t device, byte m_u8EndToEndInformationIndicator) {
        ISUP_spBackwardCallIndicator callIndicator = new ISUP_spBackwardCallIndicator();
        callIndicator.m_u8EndToEndInformationIndicator = m_u8EndToEndInformationIndicator;
        return api.XMS_ctsSetParam(g_acsHandle, device,
			(short) XMS_ISUP_Signalling_Parameter.ISUP_SP_BackwardCallIndicator, (short) 0, callIndicator);
    }
    
	/**
	 * 设置主叫信息
	 *
	 * @param device
	 * @param calledNo
	 * @return
	 */
	private int setCallerParam(DeviceID_t device, String callerNo) {       
		ISUP_spCallingPartNumber caller = new ISUP_spCallingPartNumber();
		caller.m_u8NatureOfAddressIndicator = (byte) (callerNo.startsWith("0") ? 3 : 1);
		caller.m_u8OddEvenIndicator = 0;
		caller.m_u8NumberingPlanIndicator = 1;
		caller.m_u8Screening = 3;
		caller.m_u8AddressPresentationRestrictedIndicator = 0;
		caller.m_u8NumberIncompleteIndicator = 0;
//		byte[] callerByte = new byte[callerNo.length()];
//		CharsToByte(callerByte, callerNo.toCharArray(), callerNo.length());
//		caller.m_s8AddressSignal = callerByte;
		//本地手机，如果配置了性质码，取配置的性质码
		if(Configuration.LOCAL_MOBILE > 0 && callerNo.length() == 11 && callerNo.startsWith("1")) {
			caller.m_u8NatureOfAddressIndicator = (byte) Configuration.LOCAL_MOBILE;
		}
		//判断外地手机是否把0截掉
		if (callerNo.startsWith("01") && callerNo.charAt(2) != '0' && !Configuration.CALLER_KEEP_PREFIX) {
			callerNo = callerNo.substring(1);
		}
		byte[] s8CallerNo = callerNo.getBytes();
		for (int i = 0; i < s8CallerNo.length && i < 40; i++) {
			caller.m_s8AddressSignal[i] = s8CallerNo[i];
		}
		return api.XMS_ctsSetParam(g_acsHandle, device,
			(short) XMS_ISUP_Signalling_Parameter.ISUP_SP_CallingPartNumber, (short) 0, caller);
	}

	/**
	 * 设置被叫信息
	 *
	 * @param device
	 * @param calledNo
	 * @return
	 */
	private int setCalledParam(DeviceID_t device, String calledNo) {
		ISUP_spCalledPartNumber called = new ISUP_spCalledPartNumber();
		called.m_u8NatureOfAddressIndicator = (byte) (calledNo.startsWith("0") ? 3 : 1);
		called.m_u8OddEvenIndicator = 0;
		called.m_u8NumberingPlanIndicator = 1;
		called.m_u8InternalNetworkNumberIndicator = 0;
//		byte[] calledByte = new byte[calledNo.length()];
//		CharsToByte(calledByte, calledNo.toCharArray(), calledNo.length());
//		called.m_s8AddressSignal = calledByte;
		byte[] s8CalledNo = calledNo.getBytes();
		for (int i = 0; i < s8CalledNo.length && i < 40; i++) {
			called.m_s8AddressSignal[i] = s8CalledNo[i];
		}
		return api.XMS_ctsSetParam(g_acsHandle, device,
			(short) XMS_ISUP_Signalling_Parameter.ISUP_SP_CalledPartNumber, (short) 0, called);
	}

	//会议相关api begin
	public CONF_STRUCT searchOneFreeConf(TRUNK_STRUCT oneTrunk) throws ConferenceFullException {
		CONF_STRUCT oneConf = null;
		confLock.lock();
		try {
			for (int i = 0; i < AllDeviceRes[oneTrunk.deviceID.m_s8ModuleID].lConfNum.get(); i++) {
				oneConf = AllDeviceRes[oneTrunk.deviceID.m_s8ModuleID].pConf[i];
				if (oneConf.State == CONF_STATE.CONF_FREE) {
					break;
				}
			}
			if (oneConf == null) {
				throw new ConferenceFullException("can not get one free conference device!");
			} else {
				Change_Conf_State(oneConf, CONF_STATE.CONF_OCCUPYED);
				return oneConf;
			}
		} finally {
			confLock.unlock();
		}
	}

	/**
	 * 加入会议，返回会议设备ID
	 *
	 * @param callDeviceId
	 * @param mode
	 * @param confDeviceId
	 * @return
	 * @throws com.sungoin.cti.engine.exception.ConferenceFullException
	 * @throws com.sungoin.cti.engine.exception.VoiceDeviceFullException
	 */
	public String joinToConf(String callDeviceId, int mode, String confDeviceId) throws ConferenceFullException, VoiceDeviceFullException {
		TRUNK_STRUCT oneTrunk = getTrunkStructByID(callDeviceId);
		oneTrunk.confMode = mode;
		CONF_STRUCT oneConf = null;
		if (confDeviceId != null && confDeviceId.length() > 0) {
			oneConf = getConfStructByID(confDeviceId);
		} else {
			oneConf = searchOneFreeConf(oneTrunk);
		}
		DeviceID_t freeVocDeviceID = null;
		if (mode == Constants.CONF_MODE_LISTEN && oneConf.lListenNum.get() > 0) {
			//already have listen member
			for (MEMBER_STRUCT m : oneConf.Member) {
				if (m.lMode == Constants.CONF_MODE_LISTEN) {
					freeVocDeviceID = m.DevID;
					break;
				}
			}
		} else {
			freeVocDeviceID = SearchOneFreeVoice(oneTrunk);
            if(freeVocDeviceID == null) {
                checkVocDevice();
                freeVocDeviceID = SearchOneFreeVoice(oneTrunk);
            }
            if(freeVocDeviceID == null) {
                throw new VoiceDeviceFullException("找不到空闲语音通道！");
            }
			My_DualUnlink(oneTrunk.deviceID, oneTrunk.VocDevID);
			FreeOneFreeVoice(oneTrunk.VocDevID);
			oneTrunk.VocDevID = freeVocDeviceID;
			GetVoiceStructByDevice(freeVocDeviceID.m_s8ModuleID, freeVocDeviceID.m_s16ChannelID).UsedDevID = oneTrunk.deviceID;
			oneTrunk.ConfDevID = oneConf.deviceID;

			switch (mode) {
				case Constants.CONF_MODE_ADD:
					My_DualLink(oneTrunk.deviceID, oneTrunk.VocDevID);
					myJoinToConf(oneConf.deviceID, freeVocDeviceID, Constants.CONF_MODE_ADD, oneTrunk);
					break;
				case Constants.CONF_MODE_LISTEN:
					api.XMS_ctsLinkDevice(g_acsHandle, oneTrunk.VocDevID, oneTrunk.deviceID, null);
					myJoinToConf(oneConf.deviceID, freeVocDeviceID, Constants.CONF_MODE_LISTEN, oneTrunk);
					break;
				case Constants.CONF_MODE_PLAY:
					log.warn("not support Karaok yet!");
					break;
				case Constants.CONF_MODE_SPEAKONLY:
					api.XMS_ctsLinkDevice(g_acsHandle, oneTrunk.deviceID, oneTrunk.VocDevID, null);
					myJoinToConf(oneConf.deviceID, freeVocDeviceID, Constants.CONF_MODE_SPEAKONLY, oneTrunk);
					break;
			}
		}
		return oneConf.uuid;
	}

	int myJoinToConf(DeviceID_t ConfDevID, DeviceID_t VocDevID, int mode, TRUNK_STRUCT oneTrk) {
		CONF_STRUCT oneConf = GetConfStructByDevice(ConfDevID.m_s8ModuleID, ConfDevID.m_s16ChannelID);
		CmdParamData_Conf_t confParam = new CmdParamData_Conf_t();
		switch (mode) {
			case Constants.CONF_MODE_ADD:
				confParam.m_u8InputOpt = XMS_CONF_INPUT_OPT.XMS_CONF_INPUT_OPT_NORMAL;
				confParam.m_u8OutputOpt = XMS_CONF_OUTPUT_OPT.XMS_CONF_OUTPUT_OPT_NORMAL;
				break;
			case Constants.CONF_MODE_LISTEN:
				confParam.m_u8InputOpt = XMS_CONF_INPUT_OPT.XMS_CONF_INPUT_OPT_SILENCE;
				confParam.m_u8OutputOpt = XMS_CONF_OUTPUT_OPT.XMS_CONF_OUTPUT_OPT_SUM;
				break;
			case Constants.CONF_MODE_SPEAKONLY:
				confParam.m_u8InputOpt = XMS_CONF_INPUT_OPT.XMS_CONF_INPUT_OPT_NORMAL;
				confParam.m_u8OutputOpt = XMS_CONF_OUTPUT_OPT.XMS_CONF_OUTPUT_OPT_SILENCE;
				break;
			case Constants.CONF_MODE_PLAY:
				confParam.m_u8InputOpt = XMS_CONF_INPUT_OPT.XMS_CONF_INPUT_OPT_PLAY;
//				confParam.m_u8OutputOpt = XMS_CONF_OUTPUT_OPT.XMS_CONF_OUTPUT_OPT_SILENCE;
				break;
		}

		int ret = api.XMS_ctsJoinToConf(g_acsHandle, ConfDevID, VocDevID, confParam, null);
		if (ret > 0) {
			MEMBER_STRUCT m = new MEMBER_STRUCT(mode, VocDevID, oneTrk.uuid, oneTrk.lsh);
			oneConf.addMember(m);
			if (oneConf.lMemberNum.get() == 1) {
				OccupyOneConf(oneConf);
			}
		} else {
			log.error("XMS_ctsJoinToConf() Fail in joinToConf()!");
		}
		return ret;
	}

	public int leaveConf(String callDeviceId) {
		TRUNK_STRUCT oneTrk = getTrunkStructByID(callDeviceId);
		if (oneTrk.ConfDevID == null) {
			return 0;
		} else {
			leaveConf(oneTrk);
		}
		return 1;
	}

	private void leaveConf(TRUNK_STRUCT oneTrunk) {
		CONF_STRUCT oneConf = GetConfStructByDevice(oneTrunk.ConfDevID.m_s8ModuleID, oneTrunk.ConfDevID.m_s16ChannelID);
		switch (oneTrunk.confMode) {
			case Constants.CONF_MODE_ADD:
				My_DualUnlink(oneTrunk.ConfDevID, oneTrunk.VocDevID);
				My_LeaveFromConf(oneTrunk.ConfDevID, oneTrunk.VocDevID, Constants.CONF_MODE_ADD);
				break;
			case Constants.CONF_MODE_LISTEN:
				api.XMS_ctsUnlinkDevice(g_acsHandle, oneTrunk.VocDevID, oneTrunk.deviceID, null);
				My_LeaveFromConf(oneTrunk.ConfDevID, oneTrunk.VocDevID, Constants.CONF_MODE_LISTEN);
				break;
			case Constants.CONF_MODE_PLAY:
				log.warn("not support Karaok yet !");
				break;
			case Constants.CONF_MODE_SPEAKONLY:
				api.XMS_ctsUnlinkDevice(g_acsHandle, oneTrunk.deviceID, oneTrunk.VocDevID, null);
				My_LeaveFromConf(oneTrunk.ConfDevID, oneTrunk.VocDevID, Constants.CONF_MODE_SPEAKONLY);
				break;
		}
		oneTrunk.ConfDevID = null;
		if (oneConf.lMemberNum.get() == 0) {
			if (oneConf.recordDevID != null) {
				freeVocDevice(oneConf.recordDevID);
			}
			api.XMS_ctsClearConf(g_acsHandle, oneConf.deviceID, null);
		}
	}

	private int My_LeaveFromConf(DeviceID_t confDevID, DeviceID_t vocDevId, int mode) {
		CONF_STRUCT oneConf = GetConfStructByDevice(confDevID.m_s8ModuleID, confDevID.m_s16ChannelID);
		CmdParamData_Conf_t confParam = new CmdParamData_Conf_t();
		MEMBER_STRUCT member = null;
		if (mode != Constants.CONF_MODE_PLAY) {
			confParam.m_u8InputOpt = XMS_CONF_INPUT_OPT.XMS_CONF_INPUT_OPT_NORMAL;
		} else {
			confParam.m_u8InputOpt = XMS_CONF_INPUT_OPT.XMS_CONF_INPUT_OPT_PLAY;
		}
		int ret = api.XMS_ctsLeaveFromConf(g_acsHandle, confDevID, vocDevId, confParam, null);
		if (ret > 0) {
			for (MEMBER_STRUCT m : oneConf.Member) {
				if (m.lMode == mode && isDeviceEqual(m.DevID, vocDevId)) {
					member = m;
					break;
				}
			}
			//原先C++的逻辑，移除的成员后面的往前移动一位
//				if (i < (oneConf.lMemberNum - 1)) {
//					for (j = i; j < oneConf.lMemberNum - 1; j++) {
//						oneConf.Member[j] = oneConf.Member[j + 1];
//					}
//				}
			//新的逻辑，仅仅修该会议人数，移除标志
			if (member != null) {
				oneConf.leaveMember(member);
			}
			if (oneConf.lMemberNum.get() == 0) {
				freeOneConf(oneConf.deviceID);
			}
		} else {
			log.error("XMS_ctsLeaveFromConf() Fail in My_LeaveFromConf()!");
		}
		return ret;
	}

	public void freeOneConf(DeviceID_t oneConfDeviceID) {
		if (AllDeviceRes[oneConfDeviceID.m_s8ModuleID].lFlag == 1) {
			CONF_STRUCT oneConf = GetConfStructByDevice(oneConfDeviceID.m_s8ModuleID, oneConfDeviceID.m_s16ChannelID);
			Change_Conf_State(oneConf, CONF_STATE.CONF_FREE);
			oneConf.beginTime = 0;
			AllDeviceRes[oneConfDeviceID.m_s8ModuleID].lConfFreeNum.incrementAndGet();
			g_iTotalConfFree.incrementAndGet();
		}
	}

	void OccupyOneConf(CONF_STRUCT oneConf) {
		if (oneConf.State == CONF_STATE.CONF_OCCUPYED) {
			Change_Conf_State(oneConf, CONF_STATE.CONF_USED);
			oneConf.beginTime = System.currentTimeMillis();
			AllDeviceRes[oneConf.deviceID.m_s8ModuleID].lConfFreeNum.decrementAndGet();
			g_iTotalConfFree.decrementAndGet();
		}
	}

	public void showConfInfo(int channel) {
		log.info("conf total num : {}", AllDeviceRes[1].lConfNum);
		log.info("conf opened num : {}", AllDeviceRes[1].lConfOpened);
		log.info("conf free num : {}", AllDeviceRes[1].lConfFreeNum);
		CONF_STRUCT oneConf = AllDeviceRes[1].pConf[1];
		log.info("conf state = {}", oneConf.State);
		log.info("conf lListenNum = {}", oneConf.lListenNum);
		log.info("conf lMemberNum = {}", oneConf.lMemberNum);
		log.info("conf member 1 is {}", oneConf.Member.size() > 0 ? oneConf.Member.get(0) : null);
	}

	public boolean isDeviceEqual(DeviceID_t dev1, DeviceID_t dev2) {
		return (dev1.m_s16DeviceMain == dev2.m_s16DeviceMain && dev1.m_s8ModuleID == dev2.m_s8ModuleID
			&& dev1.m_s16ChannelID == dev2.m_s16ChannelID);
	}

	//会议相关api end
	//查询统计相关
	public DeviceStatistic getStatistic(DeviceType dt) {
		int total = 0, opened = 0, used = 0, free = 0;
		switch (dt) {
			case PCM:
				dt = DeviceType.PCM;
				total = g_iTotalPcm.get();
				opened = g_iTotalPcmOpened.get();
				break;
			case TRUNK:
				dt = DeviceType.TRUNK;
				total = g_iTotalTrunk.get();
				opened = g_iTotalTrunkOpened.get();
				for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
					if (m.lFlag != 0) {
						for (TRUNK_STRUCT t : m.pTrunk) {
							if (t.uuid != null && t.iLineState != DEVICE_CALL_STATE.DCS_FREE) {
								used++;
							}
						}
					}
				}
				free = opened - used;
				break;
			case VOC:
				dt = DeviceType.VOC;
				total = g_iTotalVoice.get();
				opened = g_iTotalVoiceOpened.get();
				free = g_iTotalVoiceFree.get();
				used = opened - free;
				break;
			case CONF:
				dt = DeviceType.CONF;
				total = g_iTotalConf.get();
				opened = g_iTotalConfOpened.get();
				free = g_iTotalConfFree.get();
				used = opened - free;
				break;
			default:
				log.warn("not support this deviceType statistic : {}", dt);
				break;
		}
		return new DeviceStatistic(dt, total, opened, used, free);
	}
    
    public String getTrunkStatus() {
        StringBuilder sb = new StringBuilder();
        for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
            if (m.lFlag != 0) {
                for (TRUNK_STRUCT t : m.pTrunk) {
                    sb.append(t.uuid).append("-->").append(t.iLineState).append("-->").append(t.occupyed).append(";");
                }
            }
        }
        return sb.toString();
    }

	public List<DeviceStatistic> getAllDeviceStatistic() {
		List<DeviceStatistic> data = new ArrayList<DeviceStatistic>();
		data.add(this.getStatistic(DeviceType.PCM));//E1
		data.add(this.getStatistic(DeviceType.TRUNK));
		data.add(this.getStatistic(DeviceType.VOC));
		data.add(this.getStatistic(DeviceType.CONF));
		return data;
	}

	public int getLicenseDays() {
		if (licenseQueryTime != 0) {
			log.debug("get license days:licenseQueryTime={},licenseDays={}", licenseQueryTime, licenseDays);
			int left = (int) ((System.currentTimeMillis() - licenseQueryTime) / 1000 / 60 / 24);
			return licenseDays - left;
		}
		return -1;
	}

	//end add
	public static String getStringByByte(byte[] s8Arr) {
		return new String(s8Arr).substring(0, GetByteArrLen(s8Arr));
	}

	public static int GetByteArrLen(byte s8Arr[]) {
		int i = 0;
		while (s8Arr[i] != 0) {
			i++;
		}
		return i;
	}

	private void InitCmdParamData_CASData() {
		for (int i = 0; i < 16; i++) {
			g_Param_CAS.m_CalledTable[i] = new CAS_CalledTableDesc_t();
		}
	}

	private void InitGlobalObjectArr() {
		for (int i = 0; i < MAX_DSP_MODULE_NUMBER_OF_XMS; i++) {
			AllDeviceRes[i] = new TYPE_XMS_DSP_DEVICE_RES();

			for (int j = 0; j < MAX_VOC_RES_NUM_EACH_DSP; j++) {
				AllDeviceRes[i].pVoice[j] = new VOICE_STRUCT();
			}

			for (int j = 0; j < MAX_PCM_RES_NUM_EACH_DSP; j++) {
				AllDeviceRes[i].pPcm[j] = new PCM_STRUCT();
			}

			for (int j = 0; j < MAX_TRUNK_RES_NUM_EACH_DSP; j++) {
				AllDeviceRes[i].pTrunk[j] = new TRUNK_STRUCT();
			}

			for (int j = 0; j < MAX_CONF_RES_NUM_EACH_DSP; j++) {
				AllDeviceRes[i].pConf[j] = new CONF_STRUCT();
			}
		}

		for (int i = 0; i < MapTable_Pcm.length; i++) {
			MapTable_Pcm[i] = new TYPE_CHANNEL_MAP_TABLE();
		}

		for (int i = 0; i < MapTable_Trunk.length; i++) {
			MapTable_Trunk[i] = new TYPE_CHANNEL_MAP_TABLE();
			MapTable_Voice[i] = new TYPE_CHANNEL_MAP_TABLE();
		}

		for (int i = 0; i < MapTable_Conf.length; i++) {
			MapTable_Conf[i] = new TYPE_CHANNEL_MAP_TABLE();
		}
	}

	public void CharsToByte(byte[] DestArr, char[] SourArr, int nLen) {
		/**/

		for (int i = 0; i < nLen; i++) {
			DestArr[i] = (byte) SourArr[i];
		}
	}

	private void ReadFromConfig() {
		try {
			String strIp = Configuration.getProperty(Constants.IP_ADDR);
			CharsToByte(cfg_ServerID.m_s8ServerIp, strIp.toCharArray(), strIp.length());

			String strPort = Configuration.getProperty(Constants.PORT);
			cfg_ServerID.m_u32ServerPort = Integer.parseInt(strPort);

			String strUserName = Configuration.getProperty(Constants.USERNAME);
			CharsToByte(cfg_ServerID.m_s8UserName, strUserName.toCharArray(), strUserName.length());

			String strPwd = Configuration.getProperty(Constants.PASSWORD);
			CharsToByte(cfg_ServerID.m_s8UserPwd, strPwd.toCharArray(), strPwd.length());

			String strVocRule = Configuration.getProperty(Constants.VOICE_RULE);
			cfg_iVoiceRule = Integer.parseInt(strVocRule);

			String strDebugOn = Configuration.getProperty(Constants.DEBUG_ON);
			cfg_s32DebugOn = Integer.parseInt(strDebugOn);
		} catch (Exception ex) {
			throw new RuntimeException(ex.getMessage(), ex);
		}
	}

	boolean AddDeviceRes_Voice(DJEvtQueryDeviceData acsQueryDevice) {
		log.debug("add device voice {}", acsQueryDevice);
		int nDevNum = acsQueryDevice.acsDevListHead.m_s32DeviceNum;
		byte bDspModID = (byte) acsQueryDevice.acsDevListHead.m_s32ModuleID;
		log.debug("nDevNum {} bDspModID {}", nDevNum, bDspModID);
		if (bDspModID < 0 || bDspModID > 255) {
			log.error("Invalid DSP ID,Add Voice Resource Fail!");
			return false;
		}

		AllDeviceRes[bDspModID].lVocNum.set(nDevNum);
		AllDeviceRes[bDspModID].lVocOpened.set(0);
		AllDeviceRes[bDspModID].lVocFreeNum.set(0);

		for (int i = 0; i < nDevNum; i++) {
			AllDeviceRes[bDspModID].pVoice[i].deviceID.CopyDeviceID_tObj(acsQueryDevice.device[i]);
			AllDeviceRes[bDspModID].pVoice[i].State = VOICE_STATE.VOC_WAITOPEN;
		}
		return true;
	}

	boolean AddDeviceRes_Conf(DJEvtQueryDeviceData acsQueryDevice) {
		log.debug("add device conf {}", acsQueryDevice);
		int s32Num = acsQueryDevice.acsDevListHead.m_s32DeviceNum;
		byte s8DspModID = (byte) acsQueryDevice.acsDevListHead.m_s32ModuleID;

		AllDeviceRes[s8DspModID].lConfNum.set(s32Num);
		AllDeviceRes[s8DspModID].lConfOpened.set(0);
		AllDeviceRes[s8DspModID].lConfFreeNum.set(0);
		for (int i = 0; i < s32Num; i++) {
			AllDeviceRes[s8DspModID].pConf[i].deviceID.CopyDeviceID_tObj(acsQueryDevice.device[i]);
			AllDeviceRes[s8DspModID].pConf[i].State = CONF_STATE.CONF_WAITOPEN;
		}
		return true;
	}

	boolean AddDeviceRes_Pcm(DJEvtQueryDeviceData acsQueryDevice) {
		int nDevNum = acsQueryDevice.acsDevListHead.m_s32DeviceNum;
		byte bDspModID = (byte) acsQueryDevice.acsDevListHead.m_s32ModuleID;

		if (bDspModID < 0 || bDspModID > 255) {
			log.error("Invalid DSP ID,Add Pcm Resource Fail!");
			return false;
		}

		AllDeviceRes[bDspModID].lPcmNum.set(nDevNum);
		AllDeviceRes[bDspModID].lPcmOpened.set(0);

		for (int i = 0; i < nDevNum; i++) {
			AllDeviceRes[bDspModID].pPcm[i].deviceID.CopyDeviceID_tObj(acsQueryDevice.device[i]);
			AllDeviceRes[bDspModID].pPcm[i].bOpenFlag = false;
		}
		return true;
	}

	boolean AddDeviceRes_Board(DJEvtQueryDeviceData acsQueryDevice) {
		int nDevNum = acsQueryDevice.acsDevListHead.m_s32DeviceNum; //
		byte bDspModID = (byte) acsQueryDevice.acsDevListHead.m_s32ModuleID;

		if (bDspModID < 0 || bDspModID > 255) {
			log.error("Invalid DSP ID,Add Board Fail!");
			return false;
		}

		if (AllDeviceRes[bDspModID].lFlag == 0 && nDevNum > 0) {
			AllDeviceRes[bDspModID].deviceID.CopyDeviceID_tObj(acsQueryDevice.device[0]);
			AllDeviceRes[bDspModID].bOpenFlag = false;
			AllDeviceRes[bDspModID].bErrFlag = false;
			AllDeviceRes[bDspModID].RemoveState = REMOVE_STATE.DSP_REMOVE_STATE_NONE;
		}
		return true;
	}

	boolean AddDeviceRes_Trunk(DJEvtQueryDeviceData acsQueryDevice) {
		int nDevNum = acsQueryDevice.acsDevListHead.m_s32DeviceNum;
		byte bDspModID = (byte) acsQueryDevice.acsDevListHead.m_s32ModuleID;

		if (bDspModID < 0 || bDspModID > 255) {
			System.out.println("Invalid DSP ID,Add Board Fail!");
			return false;
		}
		AllDeviceRes[bDspModID].lTrunkNum.set(nDevNum);
		AllDeviceRes[bDspModID].lTrunkOpened.set(0);

		for (int i = 0; i < nDevNum; i++) {
			TRUNK_STRUCT oneTrk = AllDeviceRes[bDspModID].pTrunk[i];
			oneTrk.deviceID.CopyDeviceID_tObj(acsQueryDevice.device[i]);
			oneTrk.State = TRUNK_STATE.TRK_WAITOPEN;
		}
		return true;
	}

	public void OpenAllDevice_Dsp(byte bDspModID) {
		AllDeviceRes[bDspModID].bErrFlag = false; //发生过CloseDevice 事件的标志
		AllDeviceRes[bDspModID].RemoveState = REMOVE_STATE.DSP_REMOVE_STATE_NONE;

		api.XMS_ctsOpenDevice(g_acsHandle, AllDeviceRes[bDspModID].deviceID, null); //Open Board
		int count = AllDeviceRes[bDspModID].lVocNum.get();
		for (int i = 0; i < count; i++) {
			if (AllDeviceRes[bDspModID].pVoice[i].State == VOICE_STATE.VOC_WAITOPEN) { //Open Voc	
				api.XMS_ctsOpenDevice(g_acsHandle, AllDeviceRes[bDspModID].pVoice[i].deviceID, null);
			}
		}
		count = AllDeviceRes[bDspModID].lPcmNum.get();
		for (int i = 0; i < count; i++) {
			if (AllDeviceRes[bDspModID].pPcm[i].bOpenFlag == false) {
				api.XMS_ctsOpenDevice(g_acsHandle, AllDeviceRes[bDspModID].pPcm[i].deviceID, null);
			}
		}
		count = AllDeviceRes[bDspModID].lTrunkNum.get();
		for (int i = 0; i < count; i++) {
			if (AllDeviceRes[bDspModID].pTrunk[i].State == TRUNK_STATE.TRK_WAITOPEN) {
				api.XMS_ctsOpenDevice(g_acsHandle, AllDeviceRes[bDspModID].pTrunk[i].deviceID, null);
			}
		}
		count = AllDeviceRes[bDspModID].lConfNum.get();
		for (int i = 0; i < count; i++) {
			if (AllDeviceRes[bDspModID].pConf[i].State == CONF_STATE.CONF_WAITOPEN) {
				api.XMS_ctsOpenDevice(g_acsHandle, AllDeviceRes[bDspModID].pConf[i].deviceID, null);
			}
		}
	}

	PCM_STRUCT GetPcmStructByDevice(byte s8ModuleID, short s16ChannelID) {
		return AllDeviceRes[s8ModuleID].pPcm[s16ChannelID];
	}

	VOICE_STRUCT GetVoiceStructByDevice(byte s8ModuleID, short s16ChannelID) {
		return AllDeviceRes[s8ModuleID].pVoice[s16ChannelID];
	}

	TRUNK_STRUCT GetTrunkStructByDevice(byte s8ModuleID, short s16ChannelID) {
		return AllDeviceRes[s8ModuleID].pTrunk[s16ChannelID];
	}

	CONF_STRUCT GetConfStructByDevice(byte s8ModuleID, short s16ChannelID) {
		return AllDeviceRes[s8ModuleID].pConf[s16ChannelID];
	}

	public int stopPlay(String callDeviceId) {
		TRUNK_STRUCT oneTrk = this.getTrunkStructByID(callDeviceId);
		if (oneTrk.VocDevID == null) {
			log.warn("设备未关联语音通道，确认是否已振铃或应答！");
			return -1;
		}
		return StopPlayFile(oneTrk.VocDevID);
	}

	int StopPlayFile(DeviceID_t VocDevice) {
		ControlPlay_t ctrlPlay = new ControlPlay_t();
		ctrlPlay.m_u16ControlType = XMS_CTRL_PLAY_TYPE.XMS_STOP_PLAY;

		int nRet = api.XMS_ctsControlPlay(g_acsHandle, VocDevice, ctrlPlay, null);
		return nRet;
	}

	public int playList(String deviceID, String[] fileName, int taskId) throws IllegalFilePathExcaption {
		log.debug("in playList ... deviceID={},fileName={},taskId={}", deviceID, fileName, taskId);
		TRUNK_STRUCT oneTrk = this.getTrunkStructByID(deviceID);
		if (oneTrk.VocDevID == null) {
			log.warn("设备未关联语音通道，确认是否已振铃或应答！");
			return -1;
		}
		oneTrk.u8PlayTag = (byte) taskId;
		for (String name : fileName) {
			this.PlayFile(oneTrk.VocDevID, name, oneTrk.u8PlayTag, false, true, 0);
		}
		return 1;
	}

	public int playFile(String deviceID, String fileName, int taskId, boolean loop, boolean isQueue, int maxSecond) throws IllegalFilePathExcaption {
		TRUNK_STRUCT oneTrk = this.getTrunkStructByID(deviceID);
		if (oneTrk.VocDevID == null) {
			log.warn("设备未关联语音通道，确认是否已振铃或应答！");
			return -1;
		}
		oneTrk.u8PlayTag = (byte) taskId;
		return this.PlayFile(oneTrk.VocDevID, fileName, oneTrk.u8PlayTag, loop, isQueue, maxSecond);
	}

	private int PlayFile(DeviceID_t VocDevice, String FileName, byte s8PlayTag, boolean loop, boolean bIsQueue, int maxSecond) throws IllegalFilePathExcaption {
		PlayProperty_t PlayProty = new PlayProperty_t();
		//文件路径不能大于128 中文路径 byte 数组大小会变大
		if (FileName.length() > 128) {
			throw new IllegalFilePathExcaption("fileName.length can not more than 128");
		}
		if (bIsQueue) {
			PlayProty.m_u16PlayType = XMS_PLAY_TYPE.XMS_PLAY_TYPE_FILE_QUEUE;
		} else {
			PlayProty.m_u16PlayType = XMS_PLAY_TYPE.XMS_PLAY_TYPE_FILE;
		}
		byte[] byteName = FileName.getBytes();
		PlayProty.m_u8TaskID = s8PlayTag;
		for (int i = 0; i < FileName.length() && i < 128; i++) { //128 
			PlayProty.m_s8PlayContent[i] = byteName[i];
		}
		if (maxSecond <= 0) {
			if (loop) {
				PlayProty.m_u32MaxTime = Integer.MAX_VALUE;
			} else {
				PlayProty.m_u32MaxTime = 0;
			}
		} else {
			PlayProty.m_u32MaxTime = maxSecond * 100;
		}
		log.info("开始调用XMS_ctsPlay方法，文件路径：{},VocDevice={}，m_s16DeviceMain={}，m_s16DeviceSub={}，m_s8ModuleID={}，m_s8MachineID={} ", FileName, VocDevice, 
				VocDevice.m_s16DeviceMain, VocDevice.m_s16DeviceSub, VocDevice.m_s8ModuleID, VocDevice.m_s16ChannelID);
		int nRet = api.XMS_ctsPlay(g_acsHandle, VocDevice, PlayProty, null);
		return nRet;
	}

	public int record(String callDeviceId, String recordFileName, int timeMillis, boolean isAppend) throws IllegalFilePathExcaption {
		TRUNK_STRUCT oneTrk = this.getTrunkStructByID(callDeviceId);
		DeviceID_t device = oneTrk.VocDevID;
		if (device == null) {
			log.warn("设备未关联语音通道，确认是否已振铃或应答！");
			return -1;
		}
		short calleeChannel = -1;
        if(recordFileName.endsWith("_L.wav") || recordFileName.endsWith("_R.wav")) {
            return RecordFile(device, recordFileName, timeMillis, isAppend, false, calleeChannel);
        }
		if (oneTrk.relativeCallId != null) {
			TRUNK_STRUCT callee = this.getTrunkStructByID(oneTrk.relativeCallId);
			log.debug("record callee.vocDevID = {}", getDeviceInfo(callee.VocDevID));
			if (callee.VocDevID != null) {
				calleeChannel = callee.VocDevID.m_s16ChannelID;
			}
		}
		boolean inConf = oneTrk.ConfDevID != null;
		short channelID = inConf ? oneTrk.ConfDevID.m_s16ChannelID : calleeChannel;
		if (inConf) {
			CONF_STRUCT oneConf = GetConfStructByDevice(oneTrk.ConfDevID.m_s8ModuleID, oneTrk.ConfDevID.m_s16ChannelID);
			oneTrk.isConfRecord = true;
			oneTrk.isHost = true;
			if (oneConf.recordDevID != null && !isDeviceEqual(oneConf.recordDevID, oneTrk.VocDevID)) {
				stopRecordFile(oneConf.recordDevID);
				TRUNK_STRUCT old = getTrunkStructByID(oneConf.recordCallUuid);
				old.isHost = false;
			}
			oneConf.recordDevID = oneTrk.VocDevID;
			oneConf.recordCallUuid = oneTrk.uuid;
		}
		log.info("录音指令：deviceId={}，语音设备={}，录音文件={},追加录音={}", oneTrk.uuid, getDeviceInfo(device), recordFileName, isAppend);
		return RecordFile(device, recordFileName, timeMillis, isAppend, inConf, channelID);
	}
    
    public int recordCsp(String callDeviceId, String recordFileName) {
        TRUNK_STRUCT oneTrk = this.getTrunkStructByID(callDeviceId);
		DeviceID_t device = oneTrk.VocDevID;
		if (device == null) {
			log.warn("设备未关联语音通道，确认是否已振铃或应答！");
			return -1;
		}
        oneTrk.caller = "31233412";
        int ret = recordCsp(device);
		log.info("CSP录音指令：deviceId={},返回：{}", oneTrk.uuid, ret);
//        if(ret == CtiState.CTI_SUCCESS) {
//            //ASR服务开始识别
//            asrService.start();
//        }
        return ret;
    }

	int RecordFile(DeviceID_t VocDevID, String RecordFilePath, int timeMillis, boolean bIsAppend, boolean isConf, short channelID) throws IllegalFilePathExcaption {
		RecordProperty_t recordProperty = new RecordProperty_t();
		recordProperty.m_u32MaxTime = timeMillis;
		if (bIsAppend) {
			recordProperty.m_s8IsAppend = 1;
		} else {
			recordProperty.m_s8IsAppend = 0;
		}
		if (RecordFilePath.length() > 256) {
			throw new IllegalFilePathExcaption("record file.length can not more than 256");
		}
		byte[] byteName = RecordFilePath.getBytes();
		for (int i = 0; i < RecordFilePath.length() && i < 256; i++) { //256
			recordProperty.m_s8FileName[i] = byteName[i];
		}
		recordProperty.m_u8EncodeType = XMS_VOC_CODE_TYPE.XMS_Alaw_type;
		recordProperty.m_u8SRCMode = XMS_VOC_SRC_MODE.XMS_SRC_8K;

		if (isConf) {
			log.info("对会议录音...");
			recordProperty.m_s8IsMixEnable = 1;
			recordProperty.m_MixerControl.m_u8SRC1_Ctrl = XMS_MIXER_TYPE.XMS_MIXER_FROM_CONF;
			recordProperty.m_MixerControl.m_u16SRC_ChID1 = channelID;
		} else if (channelID >= 0) {
			log.info("加入被叫的语音通道：{}...", channelID);
			recordProperty.m_s8IsMixEnable = 1;
			recordProperty.m_MixerControl.m_u8SRC1_Ctrl = XMS_MIXER_TYPE.XMS_MIXER_FROM_INPUT;
			recordProperty.m_MixerControl.m_u8SRC2_Ctrl = XMS_MIXER_TYPE.XMS_MIXER_FROM_INPUT;
			recordProperty.m_MixerControl.m_u16SRC_ChID1 = VocDevID.m_s16ChannelID;
			recordProperty.m_MixerControl.m_u16SRC_ChID2 = channelID;
		} else {
			log.info("对单通道录音...");
			recordProperty.m_s8IsMixEnable = 1;
			recordProperty.m_MixerControl.m_u8SRC1_Ctrl = XMS_MIXER_TYPE.XMS_MIXER_FROM_INPUT;
			recordProperty.m_MixerControl.m_u8SRC2_Ctrl = XMS_MIXER_TYPE.XMS_MIXER_FROM_PLAY;
			recordProperty.m_MixerControl.m_u16SRC_ChID1 = VocDevID.m_s16ChannelID;
			recordProperty.m_MixerControl.m_u16SRC_ChID2 = VocDevID.m_s16ChannelID;
		}
		return api.XMS_ctsRecord(g_acsHandle, VocDevID, recordProperty, null);
	}
    
    int recordCsp(DeviceID_t VocDevID) {
        //Csp 录音不支持混音，只能单通道录音
		RecordCSPProperty_t recordCSPProperty = new RecordCSPProperty_t();
		recordCSPProperty.m_u8EncodeType = XMS_VOC_CODE_TYPE.XMS_Linear_16bit;
		recordCSPProperty.m_u8SRCMode = XMS_VOC_SRC_MODE.XMS_SRC_8K;
		recordCSPProperty.m_u16PacketLen = 4000;
		recordCSPProperty.m_u8CSPRecType = XMS_CSPREC_TYPE.XMS_CSPREC_BARGEIN;
        recordCSPProperty.m_s8IsMixEnable = 0;
		return api.XMS_ctsRecordCSP(g_acsHandle, VocDevID, recordCSPProperty, null);
	}
	
	public int stopRecord(String callDeviceId) {
		TRUNK_STRUCT oneTrk = this.getTrunkStructByID(callDeviceId);
		if (oneTrk.VocDevID == null) {
			log.warn("设备未关联语音通道，确认是否已振铃或应答！");
			return -1;
		}
		return stopRecordFile(oneTrk.VocDevID);
	}

	int stopRecordFile(DeviceID_t VocDeviceID) {
		ControlRecord_t controlRecord = new ControlRecord_t();
		controlRecord.m_u32ControlType = XMS_CTRL_RECORD_TYPE.XMS_STOP_RECORD;
		return api.XMS_ctsControlRecord(g_acsHandle, VocDeviceID, controlRecord, null);
//        return stopCspRecord(VocDeviceID);
	}

	public int stopCspRecord(String callDeviceId) {
		TRUNK_STRUCT oneTrk = this.getTrunkStructByID(callDeviceId);
		if (oneTrk.VocDevID == null) {
			log.warn("设备未关联语音通道，确认是否已振铃或应答！");
			return -1;
		}
		return stopCspRecord(oneTrk.VocDevID);
	}
	
	public int stopCspRecord(DeviceID_t VocDeviceID) {
		ControlRecord_t controlRecord = new ControlRecord_t();
		controlRecord.m_u32ControlType = XMS_CTRL_RECORD_TYPE.XMS_CSP_STOP_RECORD;
		return api.XMS_ctsControlRecordCSP(g_acsHandle, VocDeviceID, controlRecord, null);
	}
	
	public int stopRecordFileAndCspRecord(DeviceID_t VocDeviceID) {
		int ret = stopRecordFile(VocDeviceID);
		ret = stopCspRecord(VocDeviceID);
		return ret;
	}

	private void My_DualUnlink(DeviceID_t Dev1, DeviceID_t Dev2) {
		if (Dev1 != null && Dev2 != null) {
			api.XMS_ctsUnlinkDevice(g_acsHandle, Dev1, Dev2, null);
			api.XMS_ctsUnlinkDevice(g_acsHandle, Dev2, Dev1, null);
		}
	}

	private void My_DualLink(DeviceID_t Dev1, DeviceID_t Dev2) {
		if (Dev1 != null && Dev2 != null) {
			api.XMS_ctsLinkDevice(g_acsHandle, Dev1, Dev2, null);
			api.XMS_ctsLinkDevice(g_acsHandle, Dev2, Dev1, null);
		}
	}

	void FreeOneFreeVoice(DeviceID_t VocDevice) {
		log.debug("free VocDevice ...{}", getDeviceInfo(VocDevice));
		byte s8ModuleID = VocDevice.m_s8ModuleID;
		if (AllDeviceRes[s8ModuleID].lFlag == 1) {
			VOICE_STRUCT OneVoc = GetVoiceStructByDevice(VocDevice.m_s8ModuleID, VocDevice.m_s16ChannelID);
			Change_Voc_State(OneVoc, VOICE_STATE.VOC_FREE);
			AllDeviceRes[s8ModuleID].lVocFreeNum.incrementAndGet();
			g_iTotalVoiceFree.incrementAndGet();
		}
	}

	public void CheckRemoveReady(byte s8DspModID) {
		int i;

		// check device : INTERFACE_CH
		int count = AllDeviceRes[s8DspModID].lTrunkNum.get();
		for (i = 0; i < count; i++) {
			if ((AllDeviceRes[s8DspModID].pTrunk[i].State != TRUNK_STATE.TRK_WAITOPEN)
				&& (AllDeviceRes[s8DspModID].pTrunk[i].State != TRUNK_STATE.TRK_WAIT_REMOVE)) {
				return;
			}
		}

		// check device : VOICE
		count = AllDeviceRes[s8DspModID].lVocNum.get();
		for (i = 0; i < count; i++) {
			if ((AllDeviceRes[s8DspModID].pVoice[i].State != VOICE_STATE.VOC_WAITOPEN)
				&& (AllDeviceRes[s8DspModID].pVoice[i].State != VOICE_STATE.VOC_WAIT_REMOVE)) {
				return;
			}
		}
		// check device : CONF
		count = AllDeviceRes[s8DspModID].lConfNum.get();
		for (i = 0; i < count; i++) {
			if ((AllDeviceRes[s8DspModID].pConf[i].State != CONF_STATE.CONF_WAITOPEN)
				&& (AllDeviceRes[s8DspModID].pConf[i].State != CONF_STATE.CONF_WAIT_REMOVE)) {
				return;
			}
		}

		// all device in this DSP is ready for remove 
		AllDeviceRes[s8DspModID].RemoveState = REMOVE_STATE.DSP_REMOVE_STATE_READY;
	}

	void Change_State(TRUNK_STRUCT OneTrk, int iTrkState) {
		OneTrk.State = iTrkState;
		if ((AllDeviceRes[OneTrk.deviceID.m_s8ModuleID].RemoveState == REMOVE_STATE.DSP_REMOVE_STATE_START)
			&& (iTrkState == TRUNK_STATE.TRK_FREE)) {
			OneTrk.State = TRUNK_STATE.TRK_WAIT_REMOVE;
			CheckRemoveReady(OneTrk.deviceID.m_s8ModuleID);
		}
	}

	void InitTrunkChannel(TRUNK_STRUCT pOneTrk) {
		Change_State(pOneTrk, TRUNK_STATE.TRK_FREE);
		pOneTrk.init();
	}

	void freeVocDevice(DeviceID_t vocDevice) {
		if (vocDevice != null) {
			StopPlayFile(vocDevice);
			stopRecordFile(vocDevice);
			if (vocDevice.m_s16DeviceMain != 0) {
				VOICE_STRUCT oneVoc = GetVoiceStructByDevice(vocDevice.m_s8ModuleID, vocDevice.m_s16ChannelID);
				FreeOneFreeVoice(vocDevice);
				if (oneVoc.UsedDevID != null) {
					My_DualUnlink(vocDevice, oneVoc.UsedDevID);
				}
				oneVoc.UsedDevID = null;
			}
		}
	}

	void ResetTrunk(TRUNK_STRUCT pOneTrk) {
		log.debug("free TRUNK ...{}", pOneTrk);

		if (pOneTrk.relativeCallId != null) {
			log.debug("disconnet call between {} and {}", pOneTrk.uuid, pOneTrk.relativeCallId);
			this.disConnectCall(pOneTrk.uuid + "_" + pOneTrk.lsh, pOneTrk.relativeCallId);
		}

		// free the used Conf Resource
		if (pOneTrk.ConfDevID != null && pOneTrk.ConfDevID.m_s16DeviceMain != 0) {
			log.debug("to leave conf...");
			leaveConf(pOneTrk);
		}
		// free the used Voice Resource if not a host in a conf
		if (pOneTrk.VocDevID != null) {
			if (pOneTrk.isHost && pOneTrk.isConfRecord) {
				log.debug("当前挂机的用户为会议室的主持人，保留语音通道");
//				if (pOneTrk.VocDevID.m_s16DeviceMain != 0) {
//					My_DualUnlink(pOneTrk.deviceID, pOneTrk.VocDevID);
//					GetVoiceStructByDevice(pOneTrk.VocDevID.m_s8ModuleID, pOneTrk.VocDevID.m_s16ChannelID).UsedDevID = null;
//					pOneTrk.VocDevID = null;
//				}
			} else {
				StopPlayFile(pOneTrk.VocDevID);
				stopRecordFile(pOneTrk.VocDevID);
				unlinkTrunkVoick(pOneTrk);
			}
		}
		InitTrunkChannel(pOneTrk);
		// Search Free Voice for get CallerID(FSK)
		// if you needn't CallerID, ignore next line
		//、PrepareForCallerID(pOneTrk);
	}

	void unlinkTrunkVoick(TRUNK_STRUCT trunk) {
		if (trunk.VocDevID.m_s16DeviceMain != 0) {
			My_DualUnlink(trunk.deviceID, trunk.VocDevID);
			FreeOneFreeVoice(trunk.VocDevID);
			GetVoiceStructByDevice(trunk.VocDevID.m_s8ModuleID, trunk.VocDevID.m_s16ChannelID).UsedDevID = null;
			trunk.VocDevID = null;
		}
	}
	/**
	 * 通用处理事件函数
	 *
	 * @param dev
	 * @param acsEvtData
	 * @param obj
	 */
	void handlerEvent(DeviceID_t dev, Acs_Evt_t acsEvtData, Object obj) {
		TRUNK_STRUCT OneTrk = null;
		VOICE_STRUCT OneVoc = null;
		CONF_STRUCT OneConf = null;
		String deviceId = null;
		String usedDeviceId = null;
		int lsh = 0;
		EventSource source = null;
		int nEvtType = acsEvtData.m_s32EventType;
		if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH) {
			OneTrk = GetTrunkStructByDevice(dev.m_s8ModuleID, dev.m_s16ChannelID);
			deviceId = OneTrk.uuid;
			lsh = OneTrk.lsh;
			source = EventSource.TRUNK;
		} else if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_VOICE) {
			OneVoc = GetVoiceStructByDevice(dev.m_s8ModuleID, dev.m_s16ChannelID);
			if (OneVoc.UsedDevID != null && OneVoc.UsedDevID.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH) {
				OneTrk = GetTrunkStructByDevice(OneVoc.UsedDevID.m_s8ModuleID, OneVoc.UsedDevID.m_s16ChannelID);
			}
			deviceId = OneTrk == null ? OneVoc.callDeviceId : OneTrk.uuid;
			lsh = OneTrk == null ? OneVoc.callLsh : OneTrk.lsh;
			usedDeviceId = OneVoc.uuid;
			source = EventSource.VOC;
		} else if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_CONFERENCE) {
			OneConf = GetConfStructByDevice(dev.m_s8ModuleID, dev.m_s16ChannelID);
			usedDeviceId = OneConf.uuid;
			source = EventSource.CONF;
		}
		EventData event = new EventData(deviceId, lsh, usedDeviceId);
		EventType type = null;

		switch (nEvtType) {
			case XMS_EVT_TYPE.XMS_EVT_ALERTCALL: {
				type = EventType.ALERTCALL;
				DJEvtAlertCallData alertCallData = (DJEvtAlertCallData) obj;
				event.setState(alertCallData.acsAlertCallData.m_s32AcsEvtState);
				event.setErrorCode(alertCallData.acsAlertCallData.m_s32AcsEvtErrCode);
				event.setCaller(OneTrk.caller);
				event.setCallee(OneTrk.callee);
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_ANSWERCALL: {
				type = EventType.ANSWERCALL;
				DJEvtAnswerCallData answerCallData = (DJEvtAnswerCallData) obj;
				Change_State(OneTrk, TRUNK_STATE.TRK_USED);
				OneTrk.offHookTime = System.currentTimeMillis();
				event.setState(answerCallData.acsAnswerCallData.m_s32AcsEvtState);
				event.setErrorCode(answerCallData.acsAnswerCallData.m_s32AcsEvtErrCode);
				event.setCaller(OneTrk.caller);
				event.setCallee(OneTrk.callee);
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_CALLIN: {
				type = EventType.CALLIN;
				DJEvtCallInData callInData = (DJEvtCallInData) obj;
				event.setState(callInData.acsCallInData.m_s32AcsEvtState);
				event.setErrorCode(callInData.acsCallInData.m_s32AcsEvtErrCode);
				Change_State(OneTrk, TRUNK_STATE.TRK_WAIT_ANSWERCALL);
				OneTrk.lsh = getNextLsh();
				OneTrk.callLsh = getNextCallLsh();
				OneTrk.caller = getStringByByte(callInData.acsCallInData.m_s8CallingNum);
				OneTrk.callee = getStringByByte(callInData.acsCallInData.m_s8CalledNum);
				event.setCaller(OneTrk.caller);
				event.setCallee(OneTrk.callee);
				event.setModule(OneTrk.deviceID.m_s8ModuleID + "");
				event.setChannel(OneTrk.deviceID.m_s16ChannelID + "");
				event.setLsh(OneTrk.lsh);
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_CALLOUT: {
				DJEvtCallOutData callOutData = (DJEvtCallOutData) obj;
                log.info("deviceId：{}，外呼结果：{}，ErrorCode：{}，挂机原因：{}", deviceId, callOutData.acsCallOutData.m_s32AcsEvtState, callOutData.acsCallOutData.m_s32AcsEvtErrCode, callOutData.acsCallOutData.m_s32CallClearCause);
                //收到外呼结果事件，清除CALL_OUT_MAP 中的记录
                String[] callMsg = CALL_OUT_MAP.remove(deviceId);
                log.info("收到外呼事件，清除外呼缓存，设备ID：{}，返回：{}", deviceId, callMsg != null);
                type = EventType.CALLOUT;
				event.setState(callOutData.acsCallOutData.m_s32AcsEvtState);
				event.setErrorCode(callOutData.acsCallOutData.m_s32AcsEvtErrCode);
				event.setCaller(OneTrk.caller);
				event.setCallee(OneTrk.callee);
				if (callOutData.acsCallOutData.m_s32AcsEvtState == 1) {
					Change_State(OneTrk, TRUNK_STATE.TRK_USED);
					OneTrk.offHookTime = System.currentTimeMillis();
					makeCallSuccess(OneTrk);
				} else {
                    if(OneTrk.callOutLsh != OneTrk.lsh) {
                        log.warn("流水号：{}与外呼流水号：{}不同，通道被同抢", OneTrk.lsh, OneTrk.callOutLsh);
                        event.setLsh(OneTrk.callOutLsh);
                    } else {
                        ResetTrunk(OneTrk);
                    }
				}
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_CLEARCALL: {
				DJEvtClearCallData callClearData = (DJEvtClearCallData) obj;
				event.setState(callClearData.acsClearCallData.m_s32AcsEvtState);
				event.setErrorCode(callClearData.acsClearCallData.m_s32AcsEvtErrCode);
				event.setCaller(OneTrk.caller);
				event.setCallee(OneTrk.callee);
				int callLsh = OneTrk.callLsh;
				ResetTrunk(OneTrk);
				//forget why?
//				event.setCallOver(!isLshUsed(callLsh));

				type = EventType.CLEARCALL;
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_RECVIODATA: {
				DJEvtRecvIOData receiveData = (DJEvtRecvIOData) obj;
				if (receiveData.acsIOData.m_u16IoType == XMS_MEDIA_IO_TYPE.XMS_IO_TYPE_DTMF) {
					event.setState(receiveData.acsIOData.m_s32AcsEvtState);
					event.setErrorCode(receiveData.acsIOData.m_s32AcsEvtErrCode);
					event.setDtmf(new String(receiveData.recvIOData).trim());
				}
				type = EventType.DTMF;
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_PLAY: {
				type = EventType.PLAY;
				DJEvtPlayData playData = (DJEvtPlayData) obj;
				event.setState(playData.acsMediaProcData.m_s32AcsEvtState);
				event.setErrorCode(playData.acsMediaProcData.m_s32AcsEvtErrCode);
				event.setTaskId(playData.acsMediaProcData.m_u8TaskID);
                log.info("放音结束事件：deviceId={}，语音通道={}，结束状态={}，结束原因={}", event.getDeviceID(), getDeviceInfo(dev), event.getState(), event.getErrorCode());
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_RECORD: {
				type = EventType.RECORD;
				DJEvtRecordData recordData = (DJEvtRecordData) obj;
				event.setState(recordData.acsMediaProcData.m_s32AcsEvtState);
				event.setErrorCode(recordData.acsMediaProcData.m_s32AcsEvtErrCode);
				event.setTaskId(recordData.acsMediaProcData.m_u8TaskID);
				log.info("录音结束事件：deviceId={}，语音通道={}，结束状态={}，结束原因={}", event.getDeviceID(), getDeviceInfo(dev), event.getState(), event.getErrorCode());
                //ASR服务结束识别
//                asrService.finish();
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_JOINTOCONF: {
				type = EventType.JOINTOCONF;
				DJEvtJoinToConfData joinData = (DJEvtJoinToConfData) obj;
				DeviceID_t vocDevice = joinData.acsConfCtrlData.m_MediaDevice;
				MEMBER_STRUCT m = OneConf.getMember(vocDevice);
				if (m != null) {
					event.setDeviceID(m.callDeviceId);
					event.setLsh(m.callLsh);
					event.setMode(m.getModeString());
				}
				event.setState(joinData.acsConfCtrlData.m_s32AcsEvtState);
				event.setErrorCode(joinData.acsConfCtrlData.m_s32AcsEvtErrCode);
				event.setConfMembers(joinData.acsConfCtrlData.m_s32ConfMembers);
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_LEAVEFROMCONF: {
				type = EventType.LEAVEFROMCONF;
				DJEvtLeaveFromConfData leaveData = (DJEvtLeaveFromConfData) obj;
				DeviceID_t vocDevice = leaveData.acsConfCtrlData.m_MediaDevice;
				MEMBER_STRUCT m = OneConf.removeMember(vocDevice);
				if (m != null) {
					event.setDeviceID(m.callDeviceId);
					event.setLsh(m.callLsh);
					event.setMode(m.getModeString());
				}
				event.setState(leaveData.acsConfCtrlData.m_s32AcsEvtState);
				event.setErrorCode(leaveData.acsConfCtrlData.m_s32AcsEvtErrCode);
				event.setConfMembers(leaveData.acsConfCtrlData.m_s32ConfMembers);
				break;
			}

			case XMS_EVT_TYPE.XMS_EVT_CLEARCONF: {
				type = EventType.CLEARCONF;
				DJEvtClearConfData cleaarData = (DJEvtClearConfData) obj;
				event.setState(cleaarData.acsConfCtrlData.m_s32AcsEvtState);
				event.setErrorCode(cleaarData.acsConfCtrlData.m_s32AcsEvtErrCode);
				event.setConfMembers(cleaarData.acsConfCtrlData.m_s32ConfMembers);
				if (OneConf != null) {
					OneConf.clear();
				}
				break;
			}
            case XMS_EVT_TYPE.XMS_EVT_RECORDCSP: {
				DJEvtRecordCsp data = (DJEvtRecordCsp) obj;
				log.info("收到csp语音数据：语音数据包长度：{}，语音数据类型：{}，录音的号码：{}", data.acsMediaCspProcData.m_u16DataLen, 
						data.acsMediaCspProcData.m_u8DataType, OneTrk.caller);
                byte[] byteData = data.acsMediaCspProcData.m_u8StreamData;
//                asrService.processData(byteData);
				log.info("发送数据结束");
				break;
			}
		}
		if (type != null) {
			engine.processEvent(new DJKeygoeEvent(source, type, event));
		} else {
			log.warn("ignore this event to engine...");
		}
	}
    
    public void sendEvent(DJKeygoeEvent event) {
        engine.processEvent(event);
    }

	int PlayIndex(DeviceID_t VocDevID, int u16Index, byte u8PlayTag, boolean bIsQueue) {
		PlayProperty_t playProperty = new PlayProperty_t();
		if (bIsQueue) {
			playProperty.m_u16PlayType = XMS_PLAY_TYPE.XMS_PLAY_TYPE_INDEX_QUEUE;
		} else {
			playProperty.m_u16PlayType = XMS_PLAY_TYPE.XMS_PLAY_TYPE_INDEX;
		}
		playProperty.m_u8TaskID = u8PlayTag;
		playProperty.m_u16PlayIndex = (short) u16Index;
		log.info("开始调用XMS_ctsPlay方法，index={}, VocDevice={}，m_s16DeviceMain={}，m_s16DeviceSub={}，m_s8ModuleID={}，m_s8MachineID={} ", u16Index, VocDevID, 
				VocDevID.m_s16DeviceMain, VocDevID.m_s16DeviceSub, VocDevID.m_s8ModuleID, VocDevID.m_s16ChannelID);
		int nRet = api.XMS_ctsPlay(g_acsHandle, VocDevID, playProperty, null);
		return nRet;
	}

	// Special code for CAS(SS1)
	void SpecialForCas(TRUNK_STRUCT OneTrunk, int nEvtType) throws VoiceDeviceFullException {
		if (OneTrunk.deviceID.m_s16DeviceSub != XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_E1_CAS) {
			return;
		}

		if (nEvtType == XMS_EVT_TYPE.XMS_EVT_CAS_MFC_START) {
			/* start receive MFC, need a Voice Device */

			if (OneTrunk.VocDevID == null) {
				DeviceID_t FreeVocDeviceID = SearchOneFreeVoice(OneTrunk);
                if(FreeVocDeviceID == null) {
                    checkVocDevice();
                    FreeVocDeviceID = SearchOneFreeVoice(OneTrunk);
                }
                if(FreeVocDeviceID == null) {
                    throw new VoiceDeviceFullException("找不到空闲语音通道！");
                }
				if (FreeVocDeviceID != null) {
					// add next line from V2.2.0, link by Application Program
					My_DualLink(FreeVocDeviceID, OneTrunk.deviceID);

					OneTrunk.VocDevID = FreeVocDeviceID;
					GetVoiceStructByDevice(FreeVocDeviceID.m_s8ModuleID, FreeVocDeviceID.m_s16ChannelID).UsedDevID = OneTrunk.deviceID;

					g_Param_CAS.m_VocDevID = FreeVocDeviceID;
					api.XMS_ctsSetParam(g_acsHandle, OneTrunk.deviceID, (short) XMS_CAS_PARAM_TYPE.CAS_PARAM_UNIPARAM, (short) 0, g_Param_CAS);
				}
			}
		}

		if (nEvtType == XMS_EVT_TYPE.XMS_EVT_CAS_MFC_END) {
			/* receive MFC end, release Voice Device */

			// add next line from V2.2.0, unlink by Application Program
			if (OneTrunk.VocDevID != null) {
				My_DualUnlink(OneTrunk.VocDevID, OneTrunk.deviceID);
				FreeOneFreeVoice(OneTrunk.VocDevID);
				GetVoiceStructByDevice(OneTrunk.VocDevID.m_s8ModuleID, OneTrunk.VocDevID.m_s16ChannelID).UsedDevID = null;
				OneTrunk.VocDevID = null;
			}
		}
	}
	// end of code for CAS(SS1)

	void Change_Voc_State(VOICE_STRUCT OneVoc, int NewState) {
		OneVoc.State = NewState;
		if ((AllDeviceRes[OneVoc.deviceID.m_s8ModuleID].RemoveState == REMOVE_STATE.DSP_REMOVE_STATE_START)
			&& (NewState == VOICE_STATE.VOC_FREE)) {
			OneVoc.State = VOICE_STATE.VOC_WAIT_REMOVE;
			CheckRemoveReady(OneVoc.deviceID.m_s8ModuleID);
		}
	}

	void InitConfChannel(CONF_STRUCT OneConf) {
		Change_Conf_State(OneConf, CONF_STATE.CONF_FREE);
		OneConf.lListenNum.set(0);
		OneConf.lMemberNum.set(0);
	}

	void Change_Conf_State(CONF_STRUCT OneConf, int NewState) {
		OneConf.State = NewState;

		if (AllDeviceRes[OneConf.deviceID.m_s8ModuleID].RemoveState == REMOVE_STATE.DSP_REMOVE_STATE_START && NewState == CONF_STATE.CONF_FREE) {
			OneConf.State = CONF_STATE.CONF_WAIT_REMOVE;
			CheckRemoveReady(OneConf.deviceID.m_s8ModuleID);
		}
	}

	/**
	 * 排除掉模拟外线通道，模拟外线通道外呼会直接摘机
	 *
	 * @param s16DevSub
	 * @return
	 */
	public boolean IsTrunk(short s16DevSub) {
		if ((s16DevSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_E1_PCM)
			|| (s16DevSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_E1_CAS)
			|| (s16DevSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_E1_DSS1)
			|| (s16DevSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_E1_SS7_TUP)
			|| (s16DevSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_E1_SS7_ISUP) //			|| (s16DevSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_ANALOG_TRUNK)
			) {
			return true;
		}
		return false;
	}

	TRUNK_STRUCT SearchOneFreeTrunk() throws TrunkDeviceFullException {
		byte s8ModID;
		short s16ChID;
		int count = g_iTotalTrunk.get();
		trunkLock.lock();
		try {
			if(Configuration.FREE_TRUNK_RULE == 0) {
				g_iLoopStart = 0;
			}
			for (int i = 0; i < count; i++) {
				s8ModID = MapTable_Trunk[g_iLoopStart].m_s8ModuleID;
				s16ChID = MapTable_Trunk[g_iLoopStart].m_s16ChannelID;
				TRUNK_STRUCT trunk = AllDeviceRes[s8ModID].pTrunk[s16ChID];
//				log.debug("check trunk for free! lineState={},occupyed={}", trunk.iLineState, trunk.occupyed);
				if ((trunk.iLineState == DEVICE_CALL_STATE.DCS_FREE)
					//&& IsDigitTrunk(AllDeviceRes[s8ModID].pTrunk[s16ChID].deviceID.m_s16DeviceSub) )
					&& IsTrunk(trunk.deviceID.m_s16DeviceSub)
					&& !trunk.occupyed) {
					g_iLoopStart = (g_iLoopStart + 1) % count;
					trunk.occupyed = true;
					return trunk;
				}
				g_iLoopStart = (g_iLoopStart + 1) % count;
			}
            log.warn("未找到空闲线路，当前所有线路状态：{}", this.getTrunkStatus());
			throw new TrunkDeviceFullException("can not get one free trunk device in " + count + " trunks !");
		} finally {
			trunkLock.unlock();
		}
	}

	DeviceID_t SearchOneFreeVoiceByDspID(byte dspId) {
		log.info("根据dspID寻找空闲语音设备：{}", dspId);
		byte iModuleID = dspId;
		DeviceID_t FreeOneVoc;
		vocLock.lock();
		try {
			int count = AllDeviceRes[iModuleID].lVocNum.get();
			byte order = dsp_voc_order[iModuleID];
			if(order == 0) {
				dsp_voc_order[iModuleID] = 1;
				for (int i = 0; i < count; i++ ) {
					if (AllDeviceRes[iModuleID].pVoice[i].State == VOICE_STATE.VOC_FREE) {
						FreeOneVoc = AllDeviceRes[iModuleID].pVoice[i].deviceID;
						//System.out.println(FreeOneVoc.m_s16DeviceMain);
						Change_Voc_State(AllDeviceRes[iModuleID].pVoice[i], VOICE_STATE.VOC_USED);
						g_iTotalVoiceFree.decrementAndGet();
						AllDeviceRes[iModuleID].lVocFreeNum.decrementAndGet();
						return FreeOneVoc;
					}
				}
			} else {
				dsp_voc_order[iModuleID] = 0;
				for (int i = count-1; i >=0; i-- ) {
					if (AllDeviceRes[iModuleID].pVoice[i].State == VOICE_STATE.VOC_FREE) {
						FreeOneVoc = AllDeviceRes[iModuleID].pVoice[i].deviceID;
						//System.out.println(FreeOneVoc.m_s16DeviceMain);
						Change_Voc_State(AllDeviceRes[iModuleID].pVoice[i], VOICE_STATE.VOC_USED);
						g_iTotalVoiceFree.decrementAndGet();
						AllDeviceRes[iModuleID].lVocFreeNum.decrementAndGet();
						return FreeOneVoc;
					}
				}
			}
			return null;
		} finally {
			vocLock.unlock();
		}
	}

	DeviceID_t SearchOneFreeVoice(TRUNK_STRUCT OneTrk) {
		int iMostVocFreeNum = -1;
		byte iModuleID = -1;
		DeviceID_t FreeOneVoc;
		byte s8DspID = OneTrk.deviceID.m_s8ModuleID;
		log.debug(" 获得空闲语音通道。。。cfg_iVoiceRule is {}", cfg_iVoiceRule);
		if (cfg_iVoiceRule == 0) { // Fix relationship between Trunk & Voice
			if (OneTrk.iModSeqID < AllDeviceRes[s8DspID].lVocNum.get()) {
				int i = OneTrk.iModSeqID;
				if (AllDeviceRes[s8DspID].pVoice[i].State != VOICE_STATE.VOC_WAITOPEN) {
					FreeOneVoc = AllDeviceRes[s8DspID].pVoice[i].deviceID;
					Change_Voc_State(AllDeviceRes[s8DspID].pVoice[i], VOICE_STATE.VOC_USED);
					g_iTotalVoiceFree.decrementAndGet();
					AllDeviceRes[s8DspID].lVocFreeNum.decrementAndGet();
					return FreeOneVoc;
				}
			}
			return null;
		} else if (cfg_iVoiceRule == 1) { // Search in Same Module
			iModuleID = s8DspID;
		} else if (cfg_iVoiceRule == 2) { // Search in Most free resource module
			for (int i = 0; i < g_iTotalModule.get(); i++) {
                int free = AllDeviceRes[MapTable_Module[i]].lVocFreeNum.get();
                log.debug("dsp:" + MapTable_Module[i] + ", 空闲语音通道：" + free);
				if (free > iMostVocFreeNum) {
					iMostVocFreeNum = free;
					iModuleID = MapTable_Module[i];
				}
			}
            log.debug("找到最空闲的dsp：" + iModuleID + ", 空闲语音通道：" + AllDeviceRes[iModuleID].lVocFreeNum.get() + ",使用的语音通道：" + AllDeviceRes[iModuleID].lVocOpened.get());
		}
		return SearchOneFreeVoiceByDspID(iModuleID);
	}

	void SetGtD_AnalogTrunk(DeviceID_t VocDev) {
		g_CmdVoc.m_u8GtdCtrlValid = 1;						//Enable GTD
		g_CmdVoc.m_VocGtdControl.m_u8ChannelEnable = 1;		//Enable Gtd channel
		g_CmdVoc.m_VocGtdControl.m_u8DTMFEnable = 1;			// Detect DTMF
		g_CmdVoc.m_VocGtdControl.m_u8GTDEnable = 1;			// Detect GTD 
		g_CmdVoc.m_VocGtdControl.m_u8FSKEnable = 1;			// Detect FSK for receive CallerID

		g_CmdVoc.m_VocGtdControl.m_u8EXTEnable = 0x2;		// Enable PVD Detect

		g_CmdVoc.m_VocGtdControl.m_u8GTDID[0] = (byte) 'G';
		g_CmdVoc.m_VocGtdControl.m_u8GTDID[1] = (byte) 'H';
		g_CmdVoc.m_VocGtdControl.m_u8GTDID[2] = (byte) 'I';
		g_CmdVoc.m_VocGtdControl.m_u8GTDID[3] = (byte) 'J';
		g_CmdVoc.m_VocGtdControl.m_u8GTDID[4] = (byte) 'K';

		int nRet = api.XMS_ctsSetParam(g_acsHandle, VocDev, (short) XMS_VOC_PARAM_TYPE.VOC_PARAM_UNIPARAM, (short) 0, g_CmdVoc);
		log.debug("Set GTD ret = " + Integer.toString(nRet));
	}

	void PrepareForCallerID(TRUNK_STRUCT OneTrk) throws VoiceDeviceFullException {
		if (OneTrk.deviceID.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH
			&& OneTrk.deviceID.m_s16DeviceSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_ANALOG_TRUNK) {

			DeviceID_t OneFreeVoc = SearchOneFreeVoice(OneTrk);
            if(OneFreeVoc == null) {
                checkVocDevice();
                OneFreeVoc = SearchOneFreeVoice(OneTrk);
            }
            if(OneFreeVoc == null) {
                throw new VoiceDeviceFullException("找不到空闲语音通道！");
            }
			VOICE_STRUCT OneVoc = GetVoiceStructByDevice(OneFreeVoc.m_s8ModuleID, OneFreeVoc.m_s16ChannelID);
			if(OneVoc.UsedDevID != null) {
				My_DualUnlink(OneVoc.UsedDevID, OneFreeVoc);
				OneVoc.UsedDevID = null;
			}
			OneTrk.VocDevID = OneFreeVoc;
            OneVoc.UsedDevID = OneTrk.deviceID;
            My_DualLink(OneTrk.deviceID, OneTrk.VocDevID);
            SetGtD_AnalogTrunk(OneTrk.VocDevID); //Set GTG
		}
	}

	void DispEventInfo(DeviceID_t dev, Object obj, int nEvtType) {
		StringBuilder strDisplay = new StringBuilder(GetString.GetString_EvtType(nEvtType));
		switch (nEvtType) {
			case XMS_EVT_TYPE.XMS_EVT_QUERY_DEVICE: {
				DJEvtQueryDeviceData acsQueryData = (DJEvtQueryDeviceData) obj;
				strDisplay.append("(").append(acsQueryData.acsDevListHead.m_s32DeviceMain)
					.append(",").append(acsQueryData.acsDevListHead.m_s32ModuleID).append(",")
					.append(acsQueryData.acsDevListHead.m_s32DeviceNum).append(")");
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_UNIFAILURE: {
				DJEvtUnifailureData acsUnfailure = (DJEvtUnifailureData) obj;
				strDisplay.append(" ").append(GetString.GetString_ErrorCode(acsUnfailure.unifailureData.m_s32AcsEvtErrCode))
					.append("(").append(acsUnfailure.unifailureData.m_s32AcsEvtErrCode).append(")").append(getDeviceInfo(dev));
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_RECVIODATA: {
				DJEvtRecvIOData recvData = (DJEvtRecvIOData) obj;
				strDisplay.append(getDeviceInfo(dev))
					.append(" RecvDataType:").append(GetString.GetString_IODataType(recvData.acsIOData.m_u16IoType))
					.append(" RecvDataLen:").append(Integer.toString(recvData.acsIOData.m_u16IoDataLen));
				break;
			}
			default: {
				strDisplay.append(getDeviceInfo(dev));
			}
		}
		log.info(strDisplay.toString());
	}

	String getDeviceInfo(DeviceID_t dev) {
        if(dev == null) {
            return "";
        }
        StringBuilder sb = new StringBuilder("Dev=(");
        sb.append(GetString.GetString_DeviceMain(dev.m_s16DeviceMain)).append(",").append(dev.m_s8ModuleID).append(",").append(dev.m_s16ChannelID).append(")");
		return sb.toString();
	}
	
	void SetGTD_ToneParam(DeviceID_t BrdDevice) {

		short u16ParamType = -1;

		/**
		 * *Set Freq**
		 */
		u16ParamType = XMS_BOARD_PARAM_TYPE.BOARD_PARAM_SETGTDFREQ;

		g_TmpGtdFreq.m_u16Freq_Index = 0;
		g_TmpGtdFreq.m_u16Freq_Coef = g_Param_Analog.m_u16Freq0;
		api.XMS_ctsSetParam(g_acsHandle, BrdDevice, u16ParamType, (short) 0, g_TmpGtdFreq);

		g_TmpGtdFreq.m_u16Freq_Index = 1;
		g_TmpGtdFreq.m_u16Freq_Coef = g_Param_Analog.m_u16Freq1;
		api.XMS_ctsSetParam(g_acsHandle, BrdDevice, u16ParamType, (short) 0, g_TmpGtdFreq);

		/**
		 * *Set DialTone**
		 */
		u16ParamType = XMS_BOARD_PARAM_TYPE.BOARD_PARAM_SETGTDTONE;
		g_TmpGtdProto.InitGtdProtoTypeWithZero();
		g_TmpGtdProto.m_u16GtdID = 48;		// DialTone, we use GTD's ID 48
		g_TmpGtdProto.m_u16Freq_Mask = g_Param_Analog.m_u16DialTone_FreqIndexMask;
		g_TmpGtdProto.m_u16Envelope_Mode = 0;
		g_TmpGtdProto.m_u16Repeat_Count = 1;
		g_TmpGtdProto.m_u16Min_On_Time1 = (short) (g_Param_Analog.m_u16DialTone_On_Time / 15);		// the unit is 15 ms
		api.XMS_ctsSetParam(g_acsHandle, BrdDevice, u16ParamType, (short) 0, g_TmpGtdProto);

		/**
		 * *Set Ring Back Tone**
		 */
		u16ParamType = XMS_BOARD_PARAM_TYPE.BOARD_PARAM_SETGTDTONE;
		g_TmpGtdProto.InitGtdProtoTypeWithZero();
		g_TmpGtdProto.m_u16GtdID = 49;		// DialTone, we use GTD's ID 48
		g_TmpGtdProto.m_u16Freq_Mask = g_Param_Analog.m_u16RingBackTone_FreqIndexMask;
		g_TmpGtdProto.m_u16Envelope_Mode = 1;
		g_TmpGtdProto.m_u16Repeat_Count = 1;
		g_TmpGtdProto.m_u16Min_On_Time1 = (short) ((g_Param_Analog.m_u16RingBackTone_On_Time
			* (100 - g_Param_Analog.m_u16RingBackTone_TimeDeviation) / 100) / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_On_Time1 = (short) ((g_Param_Analog.m_u16RingBackTone_On_Time
			* (100 + g_Param_Analog.m_u16RingBackTone_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Min_Off_Time1 = (short) ((g_Param_Analog.m_u16RingBackTone_Off_Time
			* (100 - g_Param_Analog.m_u16RingBackTone_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_Off_Time1 = (short) ((g_Param_Analog.m_u16RingBackTone_Off_Time
			* (100 + g_Param_Analog.m_u16RingBackTone_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		api.XMS_ctsSetParam(g_acsHandle, BrdDevice, u16ParamType, (short) 0, g_TmpGtdProto);

		/**
		 * *Set Busy Tone0**
		 */
		u16ParamType = XMS_BOARD_PARAM_TYPE.BOARD_PARAM_SETGTDTONE;
		g_TmpGtdProto.InitGtdProtoTypeWithZero();
		g_TmpGtdProto.m_u16GtdID = 50;		// DialTone, we use GTD's ID 48
		g_TmpGtdProto.m_u16Freq_Mask = g_Param_Analog.m_u16BusyTone0_FreqIndexMask;
		g_TmpGtdProto.m_u16Envelope_Mode = 1;
		g_TmpGtdProto.m_u16Repeat_Count = 1;
		g_TmpGtdProto.m_u16Min_On_Time1 = (short) ((g_Param_Analog.m_u16BusyTone0_On_Time
			* (100 - g_Param_Analog.m_u16BusyTone0_TimeDeviation) / 100) / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_On_Time1 = (short) ((g_Param_Analog.m_u16BusyTone0_On_Time
			* (100 + g_Param_Analog.m_u16BusyTone0_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Min_Off_Time1 = (short) ((g_Param_Analog.m_u16BusyTone0_Off_Time
			* (100 - g_Param_Analog.m_u16BusyTone0_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_Off_Time1 = (short) ((g_Param_Analog.m_u16BusyTone0_Off_Time
			* (100 + g_Param_Analog.m_u16BusyTone0_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		api.XMS_ctsSetParam(g_acsHandle, BrdDevice, u16ParamType, (short) 0, g_TmpGtdProto);

		/**
		 * *Set Busy Tone1**
		 */
		u16ParamType = XMS_BOARD_PARAM_TYPE.BOARD_PARAM_SETGTDTONE;
		g_TmpGtdProto.InitGtdProtoTypeWithZero();
		g_TmpGtdProto.m_u16GtdID = 51;		// DialTone, we use GTD's ID 48
		g_TmpGtdProto.m_u16Freq_Mask = g_Param_Analog.m_u16BusyTone1_FreqIndexMask;
		g_TmpGtdProto.m_u16Envelope_Mode = 1;
		g_TmpGtdProto.m_u16Repeat_Count = 1;
		g_TmpGtdProto.m_u16Min_On_Time1 = (short) ((g_Param_Analog.m_u16BusyTone1_On_Time
			* (100 - g_Param_Analog.m_u16BusyTone1_TimeDeviation) / 100) / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_On_Time1 = (short) ((g_Param_Analog.m_u16BusyTone1_On_Time
			* (100 + g_Param_Analog.m_u16BusyTone1_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Min_Off_Time1 = (short) ((g_Param_Analog.m_u16BusyTone1_Off_Time
			* (100 - g_Param_Analog.m_u16BusyTone1_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_Off_Time1 = (short) ((g_Param_Analog.m_u16BusyTone1_Off_Time
			* (100 + g_Param_Analog.m_u16BusyTone1_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		api.XMS_ctsSetParam(g_acsHandle, BrdDevice, u16ParamType, (short) 0, g_TmpGtdProto);

		/**
		 * *Set Busy Tone2**
		 */
		u16ParamType = XMS_BOARD_PARAM_TYPE.BOARD_PARAM_SETGTDTONE;
		g_TmpGtdProto.InitGtdProtoTypeWithZero();
		g_TmpGtdProto.m_u16GtdID = 52;		// DialTone, we use GTD's ID 48
		g_TmpGtdProto.m_u16Freq_Mask = g_Param_Analog.m_u16BusyTone2_FreqIndexMask;
		g_TmpGtdProto.m_u16Envelope_Mode = 1;
		g_TmpGtdProto.m_u16Repeat_Count = 1;
		g_TmpGtdProto.m_u16Min_On_Time1 = (short) ((g_Param_Analog.m_u16BusyTone2_On_Time
			* (100 - g_Param_Analog.m_u16BusyTone2_TimeDeviation) / 100) / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_On_Time1 = (short) ((g_Param_Analog.m_u16BusyTone2_On_Time
			* (100 + g_Param_Analog.m_u16BusyTone2_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Min_Off_Time1 = (short) ((g_Param_Analog.m_u16BusyTone2_Off_Time
			* (100 - g_Param_Analog.m_u16BusyTone2_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		g_TmpGtdProto.m_u16Max_Off_Time1 = (short) ((g_Param_Analog.m_u16BusyTone2_Off_Time
			* (100 + g_Param_Analog.m_u16BusyTone2_TimeDeviation)) / 100 / 15);		// the unit is 15 ms
		api.XMS_ctsSetParam(g_acsHandle, BrdDevice, u16ParamType, (short) 0, g_TmpGtdProto);
	}

	void RefreshMapTable() {
		int iModuleCount = 0, iVocCount = 0, iTrkCount = 0, iPcmCount = 0, iModSeqID = 0, iConfCount = 0;

		for (int i = 0; i < MAX_DSP_MODULE_NUMBER_OF_XMS; i++) {
			if (AllDeviceRes[i].lFlag != 0) {
				AllDeviceRes[i].iSeqID = iModuleCount;
				MapTable_Module[iModuleCount] = (byte) i; //Rember all DSP moduleid
				dsp_voc_order[iModuleCount] = 0; //init voc search order
				iModuleCount++;
				int count = AllDeviceRes[i].lPcmNum.get();
				for (int j = 0; j < count; j++) {
					AllDeviceRes[i].pPcm[j].iSeqID = iPcmCount;
					MapTable_Pcm[iPcmCount].m_s8ModuleID = AllDeviceRes[i].pPcm[j].deviceID.m_s8ModuleID;
					MapTable_Pcm[iPcmCount].m_s16ChannelID = AllDeviceRes[i].pPcm[j].deviceID.m_s16ChannelID;
					iPcmCount++;
				}
				count = AllDeviceRes[i].lTrunkNum.get();
				for (int j = 0; j < count; j++) {
					AllDeviceRes[i].pTrunk[j].iSeqID = iTrkCount;
					MapTable_Trunk[iTrkCount].m_s8ModuleID = AllDeviceRes[i].pTrunk[j].deviceID.m_s8ModuleID;
					MapTable_Trunk[iTrkCount].m_s16ChannelID = AllDeviceRes[i].pTrunk[j].deviceID.m_s16ChannelID;
					iTrkCount++;

					if (AllDeviceRes[i].pTrunk[j].deviceID.m_s16DeviceSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_UNUSABLE) {
						AllDeviceRes[i].pTrunk[j].iModSeqID = iModSeqID;
						iModSeqID++;
					}
				}
				count = AllDeviceRes[i].lVocNum.get();
				for (int j = 0; j < count; j++) {
					AllDeviceRes[i].pVoice[j].iSeqID = iVocCount;
					MapTable_Voice[iVocCount].m_s8ModuleID = AllDeviceRes[i].pVoice[j].deviceID.m_s8ModuleID;
					MapTable_Voice[iVocCount].m_s16ChannelID = AllDeviceRes[i].pVoice[j].deviceID.m_s16ChannelID;
					iVocCount++;
				}
				count = AllDeviceRes[i].lConfNum.get();
				for (int j = 0; j < count; j++) {
					AllDeviceRes[i].pConf[j].iSeqID = iConfCount;
					MapTable_Conf[iConfCount].m_s8ModuleID = AllDeviceRes[i].pConf[j].deviceID.m_s8ModuleID;
					MapTable_Conf[iConfCount].m_s16ChannelID = AllDeviceRes[i].pConf[j].deviceID.m_s16ChannelID;
					iConfCount++;
				}
			}
		}

		g_iTotalModule.set(iModuleCount);
		g_iTotalTrunk.set(iTrkCount);
		g_iTotalVoice.set(iVocCount);
		g_iTotalPcm.set(iPcmCount);
		g_iTotalConf.set(iConfCount);
	}

//	void My_BuildIndex(DeviceID_t VocDevID) {
//		int r;
//		PlayProperty_t playProperty = new PlayProperty_t();
//		r = api.XMS_ctsInitPlayIndex(g_acsHandle, VocDevID, null);
//		if (r < 0) {
//			log.debug("XMS_ctsInitPlayIndex() FAIL. ret = " + Integer.toString(r));
//			return;
//		}
//
//		for (int i = 0; i < 17; i++) {
//			String FileName = null;
//			if (i == 16) {
//				playProperty.m_u16PlayType = XMS_BUILD_INDEX_TYPE.XMS_BUILD_INDEX_FILE;
//				FileName = cfg_VocPath + "\\bank.008";
//			} else {
//				playProperty.m_u16PlayType = XMS_BUILD_INDEX_TYPE.XMS_BUILD_INDEX_RAM;
//				FileName = "\\D";
//				FileName = cfg_VocPath + FileName;
//				FileName += Integer.toString(i);
//			}
//
//			CharsToByte(playProperty.m_s8PlayContent, FileName.toCharArray(), FileName.length());
//
//			r = api.XMS_ctsBuildPlayIndex(g_acsHandle, VocDevID, playProperty, null);
//			if (r < 0) {
//				log.debug("XMS_ctsBuildPlayIndex() FAIL. ret = " + Integer.toString(r));
//				return;
//			}
//		}
//	}
	void CloseDeviceOK(DeviceID_t dev) {
		AllDeviceRes[dev.m_s8ModuleID].bErrFlag = true;
		if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_BOARD) {
			AllDeviceRes[dev.m_s8ModuleID].bOpenFlag = false;
		}

		if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH) {
			TRUNK_STRUCT OneTrunk = GetTrunkStructByDevice(dev.m_s8ModuleID, dev.m_s16ChannelID);
			Change_State(OneTrunk, TRUNK_STATE.TRK_WAITOPEN);

			// modify the count
			g_iTotalTrunkOpened.decrementAndGet();
			AllDeviceRes[dev.m_s8ModuleID].lTrunkOpened.decrementAndGet();
		} else if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_VOICE) {
			VOICE_STRUCT OneVoc = GetVoiceStructByDevice(dev.m_s8ModuleID, dev.m_s16ChannelID);
			// init this Device: Voice
			Change_Voc_State(OneVoc, VOICE_STATE.VOC_WAITOPEN);

			// modify the count
			g_iTotalVoiceOpened.decrementAndGet();
			g_iTotalVoiceFree.decrementAndGet();
			AllDeviceRes[dev.m_s8ModuleID].lVocOpened.decrementAndGet();
			AllDeviceRes[dev.m_s8ModuleID].lVocFreeNum.decrementAndGet();

		} else if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_CONFERENCE) {
			CONF_STRUCT OneConf = GetConfStructByDevice(dev.m_s8ModuleID, dev.m_s16ChannelID);
			// init this Device: Voice
			Change_Conf_State(OneConf, CONF_STATE.CONF_WAITOPEN);

			// modify the count
			g_iTotalConfOpened.decrementAndGet();
			g_iTotalConfFree.decrementAndGet();
			AllDeviceRes[dev.m_s8ModuleID].lConfOpened.decrementAndGet();
			AllDeviceRes[dev.m_s8ModuleID].lConfFreeNum.decrementAndGet();
		} else if (dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_DIGITAL_PORT) {
			PCM_STRUCT OnePcm = GetPcmStructByDevice(dev.m_s8ModuleID, dev.m_s16ChannelID);
			OnePcm.bOpenFlag = false;

			// moidfy the count
			g_iTotalPcmOpened.decrementAndGet();
			AllDeviceRes[dev.m_s8ModuleID].lPcmOpened.decrementAndGet();
		}
	}

	public int getCalleeCallNum(String callee) {
		int count = 0;
		for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
			if (m.lFlag != 0) {
				for (TRUNK_STRUCT t : m.pTrunk) {
					if (t.callee.equals(callee)) {
						count++;
					}
				}
			}
		}
		return count;
	}

	public void resetDevice(DeviceType devType, String deviceId) {
		log.info("resetDevice... devType={},deviceId={}", devType, deviceId);
	}

	public int playTone(String callDeviceId, int nPlayType) {
		TRUNK_STRUCT oneTrk = getTrunkStructByID(callDeviceId);
		if (oneTrk.VocDevID != null) {
			return PlayTone(oneTrk.VocDevID, nPlayType);
		} else {
			log.warn("not have VocDevice,can not send tone!");
			return 0;
		}
	}

	private int PlayTone(DeviceID_t VocDev, int nPlayType) {
		short s16IODataLen = 0, s16IODataType = 0;
		byte IoDataBuf[] = new byte[16];
		if (nPlayType == -1) { //Stop Play TYpe
			s16IODataLen = 0;
			s16IODataType = XMS_MEDIA_IO_TYPE.XMS_IO_TYPE_GTG;
		} else {
			s16IODataType = XMS_MEDIA_IO_TYPE.XMS_IO_TYPE_GTG;
			s16IODataLen = 1;

			if (nPlayType == 0) {
				IoDataBuf[0] = (byte) 'G'; //Dial Tone
			} else if (nPlayType == 1) {
				IoDataBuf[0] = (byte) 'H'; //Ring Back Tone
			} else if (nPlayType == 2) {
				IoDataBuf[0] = (byte) 'I'; //Busy Tone
			}
		}
		return api.XMS_ctsSendIOData(g_acsHandle, VocDev, s16IODataType, s16IODataLen, IoDataBuf);
	}

	public int playDtmf(String callDeviceId, String dtmfStr) {
		TRUNK_STRUCT oneTrk = getTrunkStructByID(callDeviceId);
		if (oneTrk.VocDevID != null) {
			return PlayDtmf(oneTrk.VocDevID, dtmfStr);
		} else {
			log.warn("not have VocDevice,can not send dtmf!");
			return 0;
		}
	}

	private int PlayDtmf(DeviceID_t VocDevice, String dtmfStr) {
		short s16IODataType = 0, s16IODataLen = 0;
		s16IODataLen = (short) dtmfStr.length();
		if (s16IODataLen != 0) {
			s16IODataType = XMS_MEDIA_IO_TYPE.XMS_IO_TYPE_DTMF;
		}
		if (s16IODataLen > 16) {
			s16IODataLen = 16;
		}
		byte SendIOData[] = dtmfStr.getBytes();
		return api.XMS_ctsSendIOData(g_acsHandle, VocDevice, s16IODataType, s16IODataLen, SendIOData);
	}

	public List<LineState> getTrunkLineInfos() {
		Collection<TRUNK_STRUCT> trunks = this.ALL_TRUNK_MAP.values();
		List<LineState> list = new ArrayList<LineState>(trunks.size());
		for (TRUNK_STRUCT trunk : trunks) {
			if (IsTrunk(trunk.deviceID.m_s16DeviceSub)) {
				list.add(new LineState(trunk.deviceID.m_s8ModuleID, trunk.deviceID.m_s16ChannelID,
					GetString.GetString_LineState(trunk.iLineState)));
			}
		}
		return list;
	}

	private void checkVocDevice() {
		log.info("start check vocDevice...");
//		DeviceStatistic trunkStatistic = this.getStatistic(DeviceType.TRUNK);
//		DeviceStatistic vocStatistic = this.getStatistic(DeviceType.VOC);
//		if (vocStatistic.getUsed() - trunkStatistic.getUsed() > 10) {
			vocLock.lock();
			try {
				for (TYPE_XMS_DSP_DEVICE_RES m : AllDeviceRes) {
					if (m.lFlag != 0) {
						for (VOICE_STRUCT v : m.pVoice) {
							if (v.State == VOICE_STATE.VOC_USED) {
								boolean release = false;
								if (v.UsedDevID == null) {
									log.debug("voc used device is null need release!");
									release = true;
								} else {
									TRUNK_STRUCT t = GetTrunkStructByDevice(v.UsedDevID.m_s8ModuleID, v.UsedDevID.m_s16ChannelID);
									if (t.VocDevID == null || !isDeviceEqual(t.VocDevID, v.deviceID) || t.iLineState <= DEVICE_CALL_STATE.DCS_UNAVAILABLE) {
										log.debug("voc not equals trunks VocDevID need release!");
										release = true;
									}
								}
								if (release) {
									FreeOneFreeVoice(v.deviceID);
								}
							}
						}
					}
				}
			} finally {
				vocLock.unlock();
			}
//		}
	}
}
