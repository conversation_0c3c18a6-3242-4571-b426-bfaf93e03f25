/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti.socket;

import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-11-27
 */
public class ShctiServerHandler extends IoHandlerAdapter {

	private static final Logger LOG = LoggerFactory.getLogger(ShctiServerHandler.class);

	private final ShctiSocketServer server;

	public ShctiServerHandler(ShctiSocketServer client) {
		this.server = client;
	}

	@Override
	public void messageReceived(IoSession session, Object message) throws Exception {
		IoBuffer buffer = (IoBuffer) message;
		byte[] byteArray = new byte[buffer.limit()];
		buffer.get(byteArray, 0, buffer.limit());
		String msg = new String(byteArray);
		LOG.debug("message received : {}", msg);
		server.getMessageProcesser().process(msg);
	}

	@Override
	public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
		LOG.error("exceptionCaught method was called..." + cause.getMessage(), cause);
		session.close(true);
	}

	/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionClosed(org.apache.mina.core.session.IoSession)
	 */
	@Override
	public void sessionClosed(IoSession session) throws Exception {
		LOG.warn("sessionClosed method was called!");
	}

	/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionCreated(org.apache.mina.core.session.IoSession)
	 */
	@Override
	public void sessionCreated(IoSession session) throws Exception {
		LOG.debug("session creative : {}", session);
	}

	/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionIdle(org.apache.mina.core.session.IoSession, org.apache.mina.core.session.IdleStatus)
	 */
	@Override
	public void sessionIdle(IoSession session, IdleStatus status) throws Exception {
		if (status == IdleStatus.BOTH_IDLE) {
			LOG.info("sessionIdle close session...");
			session.close(true);
		}
	}
}
