/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.engine.impl.djkeygoe.local;

import com.sungoin.cti.engine.CTIEvent;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;

/**
 * 
 * <AUTHOR>
 * 2015-5-4
 */
public class DJ<PERSON>eygoeEvent implements CTIEvent{

	private final EventSource source;
	private final EventType type;
	private final EventData data;

	public DJKeygoeEvent(EventSource source, EventType type, EventData data) {
		this.source = source;
		this.type = type;
		this.data = data;
	}
	
	@Override
	public EventSource getEventSource() {
		return source;
	}

	@Override
	public EventType getEventType() {
		return type;
	}

	@Override
	public EventData getEventData() {
		return data;
	}

}
