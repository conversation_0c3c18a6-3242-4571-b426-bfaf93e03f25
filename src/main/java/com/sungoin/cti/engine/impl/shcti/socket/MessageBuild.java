/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti.socket;

import com.sungoin.cti.engine.EventType;
import com.sungoin.cti.engine.impl.shcti.Constants;

/**
 *
 * <AUTHOR> 2015-12-1
 */
public class MessageBuild {
	public static final String SPLIT = ",";
	public static final String SEPARATE = ":";
	public static final String SEMICOLON = ";";
	
	private static int SEQ = 0;

	public static String getStringFromBoolean(boolean flag) {
		return flag ? "1" : "0";
	}
	
	public synchronized static int getNextSeq() {
		if (SEQ == 9999) {
			SEQ = 0;
		}
		return ++SEQ;
	}

	public static String getFormatedNextSeq() {
		int seq = getNextSeq();
		if (seq >= 1000) {
			return String.valueOf(seq);
		}
		return String.format("%04d", seq);
	}

	public static String arrayToString(String[] array, String split) {
		if(array == null || array.length == 0) {
			return "";
		}
		StringBuilder sb = new StringBuilder();
		for(String s : array) {
			sb.append(s).append(split);
		}
		return sb.deleteCharAt(sb.length() -1).toString();
	}
	
	public static final String getLinkMessage() {
		return getFormatedNextSeq() + SPLIT + Constants.SYS_LINK + SEPARATE + "127.0.0.1,24680,2";
	}

	public static final String getHeartBeatMessage() {
		return getFormatedNextSeq() + SPLIT + Constants.SYS_HEARTBEAT + SEPARATE + "127.0.0.1,24680,2";
	}

	public static final String getAlertMessage(String callId) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_ALERT + SEPARATE + callId.replace("_", ",");
	}

	public static final String getAnswerMessage(String callId) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_ANSWER + SEPARATE + callId.replace("_", ",");
	}
	
	public static final String getPlayMessage(String callId, String fileName, String loop, String size) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_PLAY + SEPARATE + callId.replace("_", ",") + SPLIT 
			+ fileName.replaceAll("/", "\\\\") + SPLIT + loop + SPLIT + size;
	}
	
	public static final String getStopPlayMessage(String callId) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_STOPPLAY + SEPARATE + callId.replace("_", ",");
	}
	
	public static final String getRecordMessage(String callId, String fileName, String time, String append) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_RECORD + SEPARATE + callId.replace("_", ",") + SPLIT 
			+ fileName.replaceAll("/", "\\\\") + SPLIT + time + SPLIT + append;
	}
	
	public static final String getStopRecordMessage(String callId) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_STOPRECORD + SEPARATE + callId.replace("_", ",");
	}
	
	public static final String getOnhookMessage(String callId) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_ONHOOK + SEPARATE + callId.replace("_", ",");
	}
	
	public static final String getConnectMessage(String callerId, String calleeId) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_CONNECT + SEPARATE + callerId.replace("_", ",") + SPLIT + calleeId.replace("_", ",");
	}
	
	public static final String getDisconnectMessage(String callerId, String calleeId) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_DISCONNECT + SEPARATE + callerId.replace("_", ",") + SPLIT + calleeId.replace("_", ",");
	}
	
	public static final String getMakeCallMessage(String caller, String callee, String origCall, String type) {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_CALL + SEPARATE + caller 
			+ SPLIT + callee + SPLIT + origCall + SPLIT + type;
	}
	
	public static final String getStatisticMessage() {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_STATISTIC + SEPARATE;
	}
	
	public static final String getDeviceStateMessage() {
		return getFormatedNextSeq() + SPLIT + Constants.CMD_LINESTATE + SEPARATE;
	}
	
	public static final String getEventAnswer(String seq, EventType type, String content) {
		String code = null;
		switch (type) {
			case CALLIN:
				code = Constants.EVENT_CALLINCOME_REPLY;
				break;
			case ALERTCALL:
				code = Constants.EVENT_ALERT_REPLY;
				break;
			case ANSWERCALL:
				code = Constants.CMD_ANSWER_REPLY;
				break;
			case CALLOUT:
				code = Constants.EVENT_CALLOUT_REPLY;
				break;
			case PLAY:
				code = Constants.EVENT_PLAYEND_REPLY;
				break;
			case RECORD:
				code = Constants.EVENT_RECORDEND_REPLY;
				break;
			case DTMF:
				code = Constants.EVENT_DTMF_REPLY;
				break;
			case CLEARCALL:
				code = Constants.EVENT_ONHOOK_REPLY;
				break;
			default :
				break;
		}
		return code == null ? null : seq + "," + code + ":" + content;
	}
}
