/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.djkeygoe.local;

/**
 *
 * <AUTHOR> 2015-5-4
 */
public class TYPE_ANALOG_GTD_PARAM {
	// Freq
	public short m_u16Freq0;
	public short m_u16Freq1;

	// DailTone
	public short m_u16DialTone_FreqIndexMask;
	public short m_u16DialTone_On_Time;

	// RingBackTone
	public short m_u16RingBackTone_FreqIndexMask;
	public short m_u16RingBackTone_On_Time;
	public short m_u16RingBackTone_Off_Time;
	public short m_u16RingBackTone_TimeDeviation;		// in percentage

	// BusyTone0
	public short m_u16BusyTone0_FreqIndexMask;
	public short m_u16BusyTone0_On_Time;
	public short m_u16BusyTone0_Off_Time;
	public short m_u16BusyTone0_TimeDeviation;				// in percentage

	// BusyTone1
	public short m_u16BusyTone1_FreqIndexMask;
	public short m_u16BusyTone1_On_Time;
	public short m_u16BusyTone1_Off_Time;
	public short m_u16BusyTone1_TimeDeviation;				// in percentage

	// BusyTone2
	public short m_u16BusyTone2_FreqIndexMask;
	public short m_u16BusyTone2_On_Time;
	public short m_u16BusyTone2_Off_Time;
	public short m_u16BusyTone2_TimeDeviation;				// in percentage

	// other 
	public short m_u16NoDialToneAfterOffHook;
	public short m_u16Ringback_NoAnswerTime;

	public short m_u16SendFSKCallerIDTime;					// for Analog_User Channel
}
