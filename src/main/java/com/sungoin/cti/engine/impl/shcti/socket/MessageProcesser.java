/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti.socket;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.CTIEventProcess;
import com.sungoin.cti.engine.EngineState;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;
import com.sungoin.cti.engine.impl.shcti.Constants;
import com.sungoin.cti.engine.impl.shcti.ShctiEngine;
import com.sungoin.cti.engine.impl.shcti.ShctiEvent;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-12-7
 */
public class MessageProcesser implements Runnable {

	private static final Logger LOG = LoggerFactory.getLogger(MessageProcesser.class);
	private static final BlockingQueue<String> QUEUE = new ArrayBlockingQueue<>(256);

	private final CTIEngine engine;

	public MessageProcesser(CTIEngine engine) {
		this.engine = engine;
	}

	public void process(String msg) {
		boolean result = QUEUE.offer(msg);
		if (!result) {
			LOG.error(" MessageQueue is full !");
		}
	}

	public void start() {
		new Thread(this).start();
	}

	@Override
	public void run() {
		LOG.info("MessageProcesser start running...");
		while (engine.getState() == EngineState.RUNNING) {
			try {
				String message = QUEUE.take();
				String seq = message.substring(0, 4);
				String head = message.substring(0, 9);
				String code = message.substring(5, 9);
				String content = message.substring(10);
				switch (code) {
					case Constants.EVENT_ALERT:
					case Constants.EVENT_ANSWER:
					case Constants.EVENT_CALLINCOME:
					case Constants.EVENT_CALLOUT:
					case Constants.EVENT_ONHOOK:
					case Constants.EVENT_DTMF:
					case Constants.EVENT_PLAYEND:
					case Constants.EVENT_RECORDEND:
						processEvent(seq, code, content);
						break;
					default:
						processMessage(head, code, content);
						break;
				}
			} catch (Exception ex) {
				LOG.error(ex.getMessage(), ex);
			}
		}
	}

	private void processMessage(String messageHead, String messageCode, String content) {
		EventData edata;
		EventSource source;
		EventType type;
		int state, errorCode;
		switch (messageCode) {
			case Constants.CMD_CALL_REPLY:
				String[] data = content.split(",");
				String[] result = {data[1], data[0], data[2], data[3]};
				((ShctiEngine) engine).putCallResult(messageHead, result);
				break;
			case Constants.CMD_LINESTATE_REPLY:
				content = content.substring(2);
				data = content.split(";");
				((ShctiEngine) engine).putCallResult(messageHead, data);
				break;
			case Constants.CMD_STATISTIC_REPLY:
				content = content.substring(2);
				data = content.split(";");
				((ShctiEngine) engine).putCallResult(messageHead, data);
				break;
			case Constants.CMD_ONHOOK_REPLY:
				data = content.split(",");
				String deviceId = data[0];
				int lsh = Integer.valueOf(data[1]);
				boolean success = ((ShctiEngine) this.engine).removeActiveCall(deviceId, lsh);
				if (success) {
					edata = new EventData(deviceId, lsh, null);
					source = EventSource.TRUNK;
					type = EventType.CLEARCALL;
					state = Integer.valueOf(data[2]);
					errorCode = Integer.valueOf(data[3]);
					edata.setState(state);
					edata.setErrorCode(errorCode);
					((CTIEventProcess) engine).processEvent(new ShctiEvent(source, type, edata));
				}
				break;
			case Constants.CMD_ALERT_REPLY:
				data = content.split(",");
				edata = new EventData(data[0], Integer.valueOf(data[1]), null);
				source = EventSource.TRUNK;
				type = EventType.ALERTCALL;
				state = Integer.valueOf(data[2]);
				errorCode = Integer.valueOf(data[3]);
				edata.setState(state);
				edata.setErrorCode(errorCode);
				((CTIEventProcess) engine).processEvent(new ShctiEvent(source, type, edata));
				break;
			case Constants.CMD_ANSWER_REPLY:
				data = content.split(",");
				edata = new EventData(data[0], Integer.valueOf(data[1]), null);
				source = EventSource.TRUNK;
				type = EventType.ANSWERCALL;
				state = Integer.valueOf(data[2]);
				errorCode = Integer.valueOf(data[3]);
				edata.setState(state);
				edata.setErrorCode(errorCode);
				((CTIEventProcess) engine).processEvent(new ShctiEvent(source, type, edata));
				break;
			default:
				break;
		}
	}

	private void processEvent(String origSeq, String eventCode, String content) {
		LOG.debug("收到板卡服务器事件编号：{},事件内容：{}", eventCode, content);
		String[] data = content.split(",");
		String deviceId = data[0];
		int lsh = Integer.valueOf(data[1]);
		EventSource source = null;
		EventType type = null;
		EventData edata = new EventData(deviceId, lsh, null);
		int state, stopCode, errorCode;
		switch (eventCode) {
			case Constants.EVENT_ALERT:
				LOG.debug("振铃事件...deviceId={},lsh={}，三汇中间件忽略此事件！由振铃响应消息触发", deviceId, lsh);
				return;
//				source = EventSource.TRUNK;
//				type = EventType.ALERTCALL;
//				state = Integer.valueOf(data[2]);
//				errorCode = Integer.valueOf(data[3]);
//				edata.setState(state);
//				edata.setErrorCode(errorCode);
//				break;
			case Constants.EVENT_ANSWER:
				LOG.debug("摘机事件...deviceId={},lsh={}，三汇中间件忽略此事件！由摘机响应消息触发", deviceId, lsh);
				return;
//				source = EventSource.TRUNK;
//				type = EventType.ANSWERCALL;
//				state = Integer.valueOf(data[2]);
//				errorCode = Integer.valueOf(data[3]);
//				edata.setState(state);
//				edata.setErrorCode(errorCode);
//				break;
			case Constants.EVENT_CALLINCOME:
				LOG.debug("来电事件...deviceId={},lsh={}", deviceId, lsh);
				((ShctiEngine) this.engine).putActiveCall(deviceId, lsh);
				source = EventSource.TRUNK;
				type = EventType.CALLIN;
				String caller = data[2];
				String callee = data[3];
				String module = data[4];
				String channel = data[5];
				edata.setCaller(caller);
				edata.setCallee(callee);
				edata.setModule(module);
				edata.setChannel(channel);
				break;
			case Constants.EVENT_CALLOUT:
				LOG.debug("呼叫结束事件...deviceId={},lsh={}", deviceId, lsh);
				source = EventSource.TRUNK;
				type = EventType.CALLOUT;
				state = Integer.valueOf(data[2]);
				errorCode = Integer.valueOf(data[3]);
				edata.setState(state);
				edata.setErrorCode(errorCode);
				if(state == Constants.STATE_SUCCESS) {
					((ShctiEngine) this.engine).putActiveCall(deviceId, lsh);
				}
				break;
			case Constants.EVENT_ONHOOK:
				LOG.debug("挂机事件...deviceId={},lsh={}", deviceId, lsh);
				source = EventSource.TRUNK;
				type = EventType.CLEARCALL;
				state = Integer.valueOf(data[2]);
				errorCode = Integer.valueOf(data[3]);
				edata.setState(state);
				edata.setErrorCode(errorCode);
				break;
			case Constants.EVENT_DTMF:
				LOG.debug("收码事件...deviceId={},lsh={}", deviceId, lsh);
				source = EventSource.VOC;
				type = EventType.DTMF;
				String dtmf = data[2];
				edata.setDtmf(dtmf);
				edata.setState(1);
				break;
			case Constants.EVENT_PLAYEND:
				LOG.debug("放音结束事件...deviceId={},lsh={}", deviceId, lsh);
				source = EventSource.VOC;
				type = EventType.PLAY;
				state = Integer.valueOf(data[2]);
				stopCode = Integer.valueOf(data[3]);
				errorCode = Integer.valueOf(data[4]);
				edata.setState(state);
				edata.setStopCode(stopCode);
				edata.setErrorCode(errorCode);
				break;
			case Constants.EVENT_RECORDEND:
				LOG.debug("录音结束事件...deviceId={},lsh={}", deviceId, lsh);
				source = EventSource.VOC;
				type = EventType.RECORD;
				state = Integer.valueOf(data[2]);
				stopCode = Integer.valueOf(data[3]);
				errorCode = Integer.valueOf(data[4]);
				edata.setState(state);
				edata.setStopCode(stopCode);
				edata.setErrorCode(errorCode);
				break;
			default:
				LOG.warn("不明的事件消息编号：{}", eventCode);
				return;
		}
		((ShctiEngine) engine).getClient().sendMessage(MessageBuild.getEventAnswer(origSeq, type, content));
		if (eventCode.equals(Constants.EVENT_ONHOOK)) {
			//判断是否要先断开连接
			String relativeCall = ((ShctiEngine) engine).removeRelativeCall(deviceId);
			if (relativeCall != null) {
				String[] calls = relativeCall.split(":");
				engine.disConnectCall(calls[0], calls[1]);
			}
			//挂机事件，需要发送挂机指令挂掉本地通道，由本地通道挂机响应触发真正的挂机事件。
			((ShctiEngine) engine).onHook(deviceId + "_" + lsh);
			return;
		}
		((CTIEventProcess) engine).processEvent(new ShctiEvent(source, type, edata));
	}
}
