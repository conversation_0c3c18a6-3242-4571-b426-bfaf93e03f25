/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.CTIEvent;
import com.sungoin.cti.engine.CTIEventHandler;
import com.sungoin.cti.engine.ConferenceMode;
import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import com.sungoin.cti.engine.EngineState;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;
import com.sungoin.cti.engine.LineState;
import com.sungoin.cti.engine.MakeCallType;
import com.sungoin.cti.engine.exception.ConferenceFullException;
import com.sungoin.cti.engine.exception.IllegalFilePathExcaption;
import com.sungoin.cti.engine.exception.TrunkDeviceFullException;
import com.sungoin.cti.engine.exception.VoiceDeviceFullException;
import com.sungoin.cti.engine.impl.shsip.socket.HeartBeatThread;
import com.sungoin.cti.engine.impl.shsip.socket.MessageBuilder;
import com.sungoin.cti.engine.impl.shsip.socket.ShsipSocketClient;
import com.sungoin.cti.engine.impl.shsip.socket.SipMessage;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class ShsipEngine implements CTIEngine {

    private static final Logger LOG = LoggerFactory.getLogger(ShsipEngine.class);

    public static final String MASTER_ID = Configuration.getMasterId();
    public static final String LOCAL_CODE = Configuration.getLocalCode();
    public static final String MACHINE_NO = Configuration.getMachineNo();
    private static final String TEST_CALLER = Configuration.getTestCalller();
    public static final String EMPTY_DEVICE_ID = "000000000";

    private static int CALLIN_LSH = 9000;
    private static int CALLOUT_LSH = 0;

    private static final Object OUTCALL_LOCK = new Object();
    private static final Object INCALL_LOCK = new Object();

    private final ShsipSocketClient client;
    private volatile EngineState state = EngineState.STOPED;
    private final Map<String, Object> ACK_MESSAGES = new ConcurrentHashMap<>();

    private final Map<String, Integer> deviceLshMap = new ConcurrentHashMap<>();
    private final Map<Integer, String> outcallDeviceMap = new ConcurrentHashMap<>();
    private final Map<String, SipMessage> stopDtmfSipMessageMap = new ConcurrentHashMap<>();
    private final Map<String, Boolean> dtmfMap = new ConcurrentHashMap<>();
    private final Map<String, String> relativeMap = new ConcurrentHashMap<>();

    private CTIEventHandler eventHandler;
    private volatile boolean registerSuccess = false;

    public ShsipEngine() {
        this.client = new ShsipSocketClient(this);
    }

    public boolean isRegisterSuccess() {
        return registerSuccess;
    }

    public static int getNextCallinLsh() {
        synchronized (INCALL_LOCK) {
            if (CALLIN_LSH == 9999) {
                CALLIN_LSH = 9000;
            }
            return ++CALLIN_LSH;
        }
    }

    public static int getNextCalloutLsh() {
        synchronized (OUTCALL_LOCK) {
            if (CALLOUT_LSH == 999) {
                CALLOUT_LSH = 0;
            }
            return ++CALLOUT_LSH;
        }
    }

    public Integer getDeviceLsh(String deviceId) {
        Integer lsh = deviceLshMap.get(deviceId);
        return lsh == null ? -1 : lsh;
    }

    public void putDeviceLsh(String deviceId, int lsh) {
        deviceLshMap.put(deviceId, lsh);
    }

    public void putOutcallDevice(int lsh, String deviceId) {
        putDeviceLsh(deviceId, lsh);
        outcallDeviceMap.put(lsh, deviceId);
    }

    public String getOutcallDevice(int lsh) {
        return outcallDeviceMap.get(lsh);
    }

    public void clearCall(String deviceId) {
        Integer lsh = this.deviceLshMap.remove(deviceId);
        if (lsh != null && lsh < 1000) {
            this.outcallDeviceMap.remove(lsh);
        } else {
            this.relativeMap.remove(deviceId);
        }
        this.dtmfMap.remove(deviceId);
    }

    @Override
    public EngineState getState() {
        return state;
    }

    @Override
    public void start() {
        if (!client.initConnector()) {
            throw new IllegalStateException("连接三汇SIP服务器失败！");
        }
        state = EngineState.STARTED;
    }

    @Override
    public void init(CTIEventHandler eventHandler) {
        this.eventHandler = eventHandler;
        state = EngineState.RUNNING;
        client.getMessageProcesser().start();
        this.login();
        if (!registerSuccess) {
            this.logout();
            this.sleep(200);
            this.login();
        }
        if (!registerSuccess) {
            state = EngineState.STOPED;
            throw new IllegalStateException("注册三汇SIP服务器失败！");
        }
        new HeartBeatThread(this).start();
    }

    @Override
    public void shotdown() {
        this.logout();
        client.close();
        state = EngineState.STOPED;
    }
    
    @Override
    public int alert(String callDeviceId) throws VoiceDeviceFullException {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        int lsh = Integer.valueOf(callDeviceId.substring(callDeviceId.indexOf("_") + 1));
        EventData edata = new EventData(deviceId, lsh, null);
        EventSource source = EventSource.TRUNK;
        edata.setState(1);
        edata.setErrorCode(0);
        this.processEvent(new ShSipEvent(source, EventType.ALERTCALL, edata));      
        return 1;
    }

    @Override
    public int answer(String callDeviceId) throws VoiceDeviceFullException {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        if (getDeviceLsh(deviceId) != null) {
            client.sendMessage(MessageBuilder.getAnswerMessage(deviceId, false));
            return 1;
        }
        return 0;
    }

    @Override
    public int play(String callDeviceId, String fileName, boolean loop, boolean isQueue, int maxSecond) throws IllegalFilePathExcaption {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        int lsh = Integer.valueOf(callDeviceId.substring(callDeviceId.indexOf("_") + 1));
        if (lsh < 1000) {
            deviceId = this.outcallDeviceMap.get(lsh);
            LOG.info("根据外呼流水号：{}，获取真实设备号：{}", lsh, deviceId);
            if (deviceId == null) {
                return 0;
            }
        }
        SipMessage msg = MessageBuilder.getPlayMessage(deviceId, loop ? 0 : 1, 1, fileName);
        if (this.dtmfMap.containsKey(deviceId)) {
            stopDtmfAndSendMessage(deviceId, msg);
        } else {
            client.sendMessage(msg);
        }
        return 1;
    }

    public void stopDtmfAndSendMessage(String deviceId, SipMessage message) {
        stopDtmfSipMessageMap.put(deviceId, message);
        client.sendMessage(MessageBuilder.getControlMessage(deviceId));
    }

    @Override
    public int stopPlay(String callDeviceId) {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        int lsh = Integer.valueOf(callDeviceId.substring(callDeviceId.indexOf("_") + 1));
        if (lsh < 1000) {
            deviceId = this.outcallDeviceMap.get(lsh);
            LOG.info("根据外呼流水号：{}，获取真实设备号：{}", lsh, deviceId);
            if (deviceId == null) {
                return 0;
            }
        }
        client.sendMessage(MessageBuilder.getStopPlayMessage(deviceId));
        return 1;
    }

    @Override
    public int record(String callDeviceId, String fileName, int length, boolean isAppend) throws IllegalFilePathExcaption {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        int lsh = Integer.valueOf(callDeviceId.substring(callDeviceId.indexOf("_") + 1));
        String calleeDeviceId = null;//被叫
        if (lsh < 1000) {
            deviceId = this.outcallDeviceMap.get(lsh);
            LOG.info("根据外呼流水号：{}，获取真实设备号：{}", lsh, deviceId);
            if (deviceId == null) {
                return 0;
            }
            calleeDeviceId = deviceId;
        } else {
            calleeDeviceId = this.relativeMap.get(deviceId);
        }
        if (length == 0) {
            length = 9900;
        }
        boolean separate = fileName.endsWith("_L.wav") || fileName.endsWith("_R.wav");
        client.sendMessage(MessageBuilder.getRecordMessage(deviceId, length, fileName, isAppend, separate));
        if (calleeDeviceId != null) {
            this.sleep(200);
            this.receiveDtmf(calleeDeviceId);
        }
        return 1;
    }

    @Override
    public int stopRecord(String callDeviceId) {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        int lsh = Integer.valueOf(callDeviceId.substring(callDeviceId.indexOf("_") + 1));
        if (lsh < 1000) {
            deviceId = this.outcallDeviceMap.get(lsh);
            LOG.info("根据外呼流水号：{}，获取真实设备号：{}", lsh, deviceId);
            if (deviceId == null) {
                return 0;
            }
        }
        client.sendMessage(MessageBuilder.getStopRecordMessage(deviceId));
        return 1;
    }

    /**
     * 内部挂机命令
     *
     * @param deviceId
     */
    public void onHookInternal(String deviceId) {
        client.sendMessage(MessageBuilder.getOnhookMessage(deviceId, null));
    }

    @Override
    public int onHook(String callDeviceId) {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        int lsh = Integer.valueOf(callDeviceId.substring(callDeviceId.indexOf("_") + 1));
        if (lsh < 1000) {
            //外呼通道
            String realDeviceId = this.outcallDeviceMap.get(lsh);
            LOG.info("根据外呼流水号：{}，获取真实设备号：{}", lsh, realDeviceId);
            if (realDeviceId == null) {
                client.sendMessage(MessageBuilder.getOnhookMessage(ShsipEngine.EMPTY_DEVICE_ID, lsh));
            } else {
                client.sendMessage(MessageBuilder.getOnhookMessage(realDeviceId, null));
            }
        } else {
            //呼入通道
            client.sendMessage(MessageBuilder.getOnhookMessage(deviceId, null));
        }
        return 1;
    }

    @Override
    public String createAndJoinConf(String arg0, ConferenceMode arg1) throws VoiceDeviceFullException, ConferenceFullException {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String joinConf(String arg0, ConferenceMode arg1, String arg2) throws VoiceDeviceFullException, ConferenceFullException {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public String[] makeCall(MakeCallType type, String caller, String callee) throws VoiceDeviceFullException, TrunkDeviceFullException {
        return this.makeCall(type, caller, callee, null, null);
    }

    @Override
    public String[] makeCall(MakeCallType type, String caller, String callee, String origCallee, String redirectNo) throws VoiceDeviceFullException, TrunkDeviceFullException {
        int lsh = getNextCalloutLsh();
        if (StringUtils.isNotEmpty(TEST_CALLER)) {
            caller = TEST_CALLER;
        }
        client.sendMessage(MessageBuilder.getCallOutMessage(caller, callee, lsh, 90));
        return new String[]{lsh + "", EMPTY_DEVICE_ID, "0", "0"};
    }

    @Override
    public int connectCall(String callerDeviceID, String calleeDeviceId) {
        String callerId = callerDeviceID.substring(0, callerDeviceID.indexOf("_"));
        int callerLsh = Integer.valueOf(callerDeviceID.substring(callerDeviceID.indexOf("_") + 1));
        String calleeId = calleeDeviceId.substring(0, calleeDeviceId.indexOf("_"));
        int calleeLsh = Integer.valueOf(calleeDeviceId.substring(calleeDeviceId.indexOf("_") + 1));
        if (callerLsh < 1000) {
            callerId = this.outcallDeviceMap.get(callerLsh);
        }
        if (calleeLsh < 1000) {
            calleeId = this.outcallDeviceMap.get(calleeLsh);
        }
        if (StringUtils.isEmpty(callerId) || StringUtils.isEmpty(calleeId)) {
            return 0;
        }
        relativeMap.put(callerId, calleeId);
        client.sendMessage(MessageBuilder.getConnectMessage(callerId, calleeId));
        return 1;
    }

    @Override
    public int connectCall(String callerDeviceID, String calleeDeviceId, boolean sameDsp) {
        return this.connectCall(callerDeviceID, calleeDeviceId);
    }

    @Override
    public int disConnectCall(String callerDeviceID, String calleeDeviceId) {
        String callerId = callerDeviceID.substring(0, callerDeviceID.indexOf("_"));
        int callerLsh = Integer.valueOf(callerDeviceID.substring(callerDeviceID.indexOf("_") + 1));
        String calleeId = calleeDeviceId.substring(0, calleeDeviceId.indexOf("_"));
        int calleeLsh = Integer.valueOf(calleeDeviceId.substring(calleeDeviceId.indexOf("_") + 1));
        if (callerLsh < 1000) {
            callerId = this.outcallDeviceMap.get(callerLsh);
        }
        if (calleeLsh < 1000) {
            calleeId = this.outcallDeviceMap.get(calleeLsh);
        }
        if (StringUtils.isEmpty(callerId) || StringUtils.isEmpty(calleeId)) {
            return 0;
        }
        client.sendMessage(MessageBuilder.getDisConnectMessage(callerId, calleeId));
        return 1;
    }

    @Override
    public DeviceStatistic getDeviceStatistic(DeviceType type) {
        return new DeviceStatistic(type, 0, 0, 0, 0);
    }

    @Override
    public List<DeviceStatistic> getAllDeviceStatistic() {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public int getCalleeCallNum(String arg0) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public void resetDevice(DeviceType arg0, String arg1) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public int playList(String callDeviceId, String[] fileNames) throws IllegalFilePathExcaption {
        String deviceId = callDeviceId.substring(0, callDeviceId.indexOf("_"));
        int lsh = Integer.valueOf(callDeviceId.substring(callDeviceId.indexOf("_") + 1));
        if (lsh < 1000) {
            deviceId = this.outcallDeviceMap.get(lsh);
            LOG.info("根据外呼流水号：{}，获取真实设备号：{}", lsh, deviceId);
            if (deviceId == null) {
                return 0;
            }
        }
        String files = StringUtils.join(fileNames, " ");
        SipMessage msg = MessageBuilder.getPlayMessage(deviceId, 1, fileNames.length, files);
        if (this.dtmfMap.containsKey(deviceId)) {
            stopDtmfAndSendMessage(deviceId, msg);
        } else {
            client.sendMessage(msg);
        }
        return 1;
    }

    @Override
    public int leaveConf(String arg0) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public int getLicenseDays() {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public int playDtmf(String arg0, String arg1) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public int playTone(String arg0, int arg1) {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    @Override
    public List<LineState> getTrunkLineState() {
        throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
    }

    public void receiveDtmf(String deviceId) {
        this.dtmfMap.putIfAbsent(deviceId, Boolean.TRUE);
        client.sendMessage(MessageBuilder.getDtmfMessage(deviceId, 1));
    }

    public void stopReceiveDtmf(String deviceId) {
        this.dtmfMap.remove(deviceId);
        SipMessage msg = this.stopDtmfSipMessageMap.remove(deviceId);
        if (msg != null) {
            client.sendMessage(msg);
        }
    }

    @Override
    public void processEvent(CTIEvent event) {
        LOG.info("engine event come : event source = {},event type = {}, event data = {}",
                event.getEventSource(), event.getEventType(), event.getEventData());
        switch (event.getEventSource()) {
            case TRUNK:
                switch (event.getEventType()) {
                    case CALLIN:
                        //来电事件
                        eventHandler.callIncome(event);
                        break;
                    case ALERTCALL:
                        //振铃事件
                        eventHandler.alert(event);
                        break;
                    case ANSWERCALL:
                        //摘机事件
                        eventHandler.offHook(event);
                        break;
                    case CLEARCALL:
                        //挂机事件
                        eventHandler.onHook(event);
                        break;
                    case CALLOUT:
                        //外呼结果事件
                        eventHandler.callOut(event);
                        break;
                    default:
                        LOG.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
                }
                break;
            case VOC:
                switch (event.getEventType()) {
                    case PLAY:
                        //放音结束事件
                        eventHandler.playEnd(event);
                        break;
                    case DTMF:
                        //放音结束事件
                        eventHandler.receiveDTMF(event);
                        break;
                    case RECORD:
                        //放音结束事件
                        eventHandler.recordEnd(event);
                        break;
                    default:
                        LOG.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
                }
                break;
            case CONF:
                switch (event.getEventType()) {
                    case JOINTOCONF:
                        //加入会议事件
                        eventHandler.joinToConf(event);
                        break;
                    case LEAVEFROMCONF:
                        //离开会议事件
                        eventHandler.leaveConf(event);
                        break;
                    case CLEARCONF:
                        //会议结束事件
                        eventHandler.clearConf(event);
                        break;
                    default:
                        LOG.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
                }
                break;
            default:
                LOG.warn("unknow eventSource : {}", event.getEventSource());
        }
    }

    public void putAckMessage(String key, Object value) {
        this.ACK_MESSAGES.put(key, value);
    }

    public void heartBeat() {
        this.client.sendMessage(MessageBuilder.getHeartBeatMessage());
    }

    private void sleep(long time) {
        try {
            Thread.sleep(time);
        } catch (Exception e) {
        }
    }

    public void login() {
        register();
        //等待服务端返回消息
        String key = LOCAL_CODE + Constants.COMMAND_REGISTER_ACK + MASTER_ID;
        int retryCount = 3;
        this.sleep(1000);
        while (!ACK_MESSAGES.containsKey(key) && --retryCount >= 0) {
            this.sleep(1000);
        }
        Boolean result = (Boolean) ACK_MESSAGES.remove(key);
        this.registerSuccess = result == null ? false : result;
    }

    public void register() {
        this.client.sendMessage(MessageBuilder.getRegisterMessage());
    }

    public void logout() {
        this.client.sendMessage(MessageBuilder.getLogoutMessage());
    }

    @Override
    public int broadCast(String callDeviceId, String fileName) {
        LOG.warn("暂未实现广播功能！");
        return 1;
    }

    @Override
    public int detectSpeech(String callDeviceId, String file, long timeout) {
        LOG.warn("暂未实现语音识别功能！");
        return 1;
    }

    @Override
    public int stopDetectSpeech(String callDeviceId) {
        LOG.warn("暂未实现语音识别功能！");
        return 1;
    }
}
