/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti;

import com.sungoin.cti.engine.*;
import com.sungoin.cti.engine.exception.*;
import com.sungoin.cti.engine.impl.shcti.socket.MessageBuild;
import com.sungoin.cti.engine.impl.shcti.socket.ShctiSocketClient;
import com.sungoin.cti.engine.impl.shcti.socket.ShctiSocketServer;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-11-26
 */
public class ShctiEngine implements CTIEngine {

	private static final Logger log = LoggerFactory.getLogger(ShctiEngine.class);
	private volatile EngineState state = EngineState.STOPED;
	private static final long SIZE_PER_SECOND = (long) (8.5 * 1024);
	private final Map<String, String[]> CALL_RESULT_MAP = new ConcurrentHashMap<>();

	private final ShctiSocketClient client = new ShctiSocketClient();
	private final ShctiSocketServer server = new ShctiSocketServer(this);
	private CTIEventHandler eventHandler;

	private final Map<String, String> relativeMap = new ConcurrentHashMap<>();

	private final Map<String, Boolean> activeCallMap = new ConcurrentHashMap<>();
	private final Map<String, Boolean> activeDeviceMap = new ConcurrentHashMap<>();

	public String removeRelativeCall(String callId) {
		return relativeMap.remove(callId);
	}

	public ShctiSocketClient getClient() {
		return client;
	}

	public ShctiSocketServer getServer() {
		return server;
	}

	public void putCallResult(String key, String[] data) {
		CALL_RESULT_MAP.put(key, data);
	}

	public boolean callResultContains(String key) {
		return CALL_RESULT_MAP.containsKey(key);
	}

	public String[] removeCallResult(String key) {
		return CALL_RESULT_MAP.remove(key);
	}

	public void putActiveCall(String deviceId, int lsh) {
		putActiveDevice(deviceId);
		activeCallMap.put(deviceId + "_" + lsh, Boolean.TRUE);
	}

	public boolean removeActiveCall(String deviceId, int lsh) {
		boolean success = removeActiveDevice(deviceId);
		if(success) {
			activeCallMap.remove(deviceId + "_" + lsh);
		}
		return success;
	}

	public boolean isCallActive(String key) {
		return activeCallMap.containsKey(key);
	}

	private void putActiveDevice(String key) {
		activeDeviceMap.put(key, Boolean.TRUE);
	}

	private boolean removeActiveDevice(String key) {
		return activeDeviceMap.remove(key) != null;
	}

	private boolean isDeviceActive(String key) {
		return activeDeviceMap.containsKey(key);
	}

	@Override
	public EngineState getState() {
		return state;
	}

	@Override
	public void start() {
		if (!server.initServer()) {
			throw new IllegalStateException("监听本地端口失败！");
		}
		if (!client.initConnector()) {
			throw new IllegalStateException("连接三汇板卡服务器失败！");
		}
		client.sendMessage(MessageBuild.getLinkMessage());
		state = EngineState.STARTED;
	}

	@Override
	public void init(CTIEventHandler eventHandler) {
		state = EngineState.RUNNING;
		this.eventHandler = eventHandler;
		server.getMessageProcesser().start();
	}

	@Override
	public void shotdown() {
		state = EngineState.STOPED;
		client.close();
	}

	@Override
	public int alert(String callDeviceId) throws VoiceDeviceFullException {
		if (isCallActive(callDeviceId)) {
			client.sendMessage(MessageBuild.getAlertMessage(callDeviceId));
			return 1;
		}
		return 0;
	}

	@Override
	public int answer(String callDeviceId) throws VoiceDeviceFullException {
		if (isCallActive(callDeviceId)) {
			client.sendMessage(MessageBuild.getAnswerMessage(callDeviceId));
			return 1;
		}
		return 0;
	}

	@Override
	public int play(String callDeviceId, String fileName, boolean loop, boolean isQueue, int maxSecond) throws IllegalFilePathExcaption {
		client.sendMessage(MessageBuild.getPlayMessage(callDeviceId, fileName, MessageBuild.getStringFromBoolean(loop),
			maxSecond <= 0 ? "-1" : maxSecond * SIZE_PER_SECOND + ""));
		return 1;
	}

	@Override
	public int stopPlay(String callDeviceId) {
		client.sendMessage(MessageBuild.getStopPlayMessage(callDeviceId));
		return 1;
	}

	@Override
	public int record(String callDeviceId, String fileName, int length, boolean isAppend) throws IllegalFilePathExcaption {
		client.sendMessage(MessageBuild.getRecordMessage(callDeviceId, fileName, length <= 0 ? "-1" : length + "", MessageBuild.getStringFromBoolean(isAppend)));
		return 1;
	}

	@Override
	public int stopRecord(String callDeviceId) {
//		client.sendMessage(MessageBuild.getStopRecordMessage(callDeviceId));
		return 1;
	}

	@Override
	public int onHook(String callDeviceId) {
		client.sendMessage(MessageBuild.getOnhookMessage(callDeviceId));
		return 1;
	}

	@Override
	public String createAndJoinConf(String callDeviceId, ConferenceMode mode) throws VoiceDeviceFullException, ConferenceFullException {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public String joinConf(String callDeviceId, ConferenceMode mode, String confDeviceId) throws VoiceDeviceFullException, ConferenceFullException {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public String[] makeCall(MakeCallType type, String caller, String callee) throws VoiceDeviceFullException, TrunkDeviceFullException {
		return this.makeCall(type, caller, callee, null, null);
	}

	@Override
	public String[] makeCall(MakeCallType type, String caller, String callee, String origCallee, String redirectNo) throws VoiceDeviceFullException, TrunkDeviceFullException {
		int callerNative = caller.startsWith("0") ? 1 : 0;
		int calleeNative = callee.startsWith("0") ? 1 : 0;
		//处理本地手机地址码
		if (Configuration.LOCAL_MOBILE > 0 && caller.length() == 11 && caller.startsWith("1")) {
			callerNative = Configuration.LOCAL_MOBILE;
		}
		String model = callerNative + "" + calleeNative;
		if (caller.startsWith("01") && caller.charAt(2) != '0') {
			caller = caller.substring(1);
		}
        String fixedOrigCallee = "";
        if(origCallee != null) {
            fixedOrigCallee = origCallee + ";" + redirectNo;
        }
		String message = MessageBuild.getMakeCallMessage(caller, callee, fixedOrigCallee, model);
		String key = message.substring(0, 4) + MessageBuild.SPLIT + Constants.CMD_CALL_REPLY;
		client.sendMessage(message);
		String[] data = null;
		try {
			Thread.sleep(200);
			//尝试获取结果
			if (this.callResultContains(key)) {
				data = this.removeCallResult(key);
			} else {
				Thread.sleep(200);
				if (this.callResultContains(key)) {
					data = this.removeCallResult(key);
				}
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
		if (data == null) {
			throw new IllegalStateException("等待呼叫指令响应超时！");
		}
		return data;
	}

	@Override
	public int connectCall(String callerDeviceID, String calleeDeviceId) {
		client.sendMessage(MessageBuild.getConnectMessage(callerDeviceID, calleeDeviceId));
		String relativeContent = callerDeviceID + ":" + calleeDeviceId;
		relativeMap.put(callerDeviceID.substring(0, callerDeviceID.indexOf("_")), relativeContent);
		relativeMap.put(calleeDeviceId.substring(0, calleeDeviceId.indexOf("_")), relativeContent);
		return 1;
	}
    
    @Override
	public int connectCall(String callerDeviceID, String calleeDeviceId, boolean sameDsp) {
		return connectCall(callerDeviceID, calleeDeviceId);
	}

	@Override
	public int disConnectCall(String callerDeviceID, String calleeDeviceId) {
		client.sendMessage(MessageBuild.getDisconnectMessage(callerDeviceID, calleeDeviceId));
		return 1;
	}

	@Override
	public DeviceStatistic getDeviceStatistic(DeviceType dt) {
		DeviceStatistic ds = null;
		if (dt == DeviceType.TRUNK) {
			String message = MessageBuild.getStatisticMessage();
			String key = message.substring(0, 4) + MessageBuild.SPLIT + Constants.CMD_STATISTIC_REPLY;
			client.sendMessage(message);
			String[] datas = null;
			try {
				Thread.sleep(200);
				//尝试获取结果
				if (this.callResultContains(key)) {
					datas = this.removeCallResult(key);
				} else {
					Thread.sleep(200);
					if (this.callResultContains(key)) {
						datas = this.removeCallResult(key);
					}
				}
				if (datas != null) {
					int free = Integer.valueOf(datas[0].substring(5));
					int used = Integer.valueOf(datas[1].substring(5));
					int unavaliable = Integer.valueOf(datas[2].substring(12));
					int total = free + used + unavaliable;
					ds = new DeviceStatistic(dt, total, total, used, free);
				}
			} catch (Exception ex) {
				log.error(ex.getMessage(), ex);
			}
		}
		if (ds == null) {
			ds = new DeviceStatistic(dt, 0, 0, 0, 0);
		}
		return ds;
	}

	@Override
	public List<DeviceStatistic> getAllDeviceStatistic() {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public int getCalleeCallNum(String callee) {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public void resetDevice(DeviceType devType, String deviceId) {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public int playList(String callDeviceId, String[] fileNames) throws IllegalFilePathExcaption {
		String files = MessageBuild.arrayToString(fileNames, MessageBuild.SEMICOLON);
		return this.play(callDeviceId, files, false, true, 0);
	}

	@Override
	public int leaveConf(String callDeviceId) {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public int getLicenseDays() {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public int playDtmf(String callDeviceId, String dtmfStr) {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public int playTone(String callDeviceId, int nPlayType) {
		throw new UnsupportedOperationException("Not supported yet."); //To change body of generated methods, choose Tools | Templates.
	}

	@Override
	public List<LineState> getTrunkLineState() {
		String message = MessageBuild.getDeviceStateMessage();
		String key = message.substring(0, 4) + MessageBuild.SPLIT + Constants.CMD_LINESTATE_REPLY;
		client.sendMessage(message);
		String[] datas = null;
		List<LineState> states = new ArrayList<>();
		try {
			Thread.sleep(200);
			//尝试获取结果
			if (this.callResultContains(key)) {
				datas = this.removeCallResult(key);
			} else {
				Thread.sleep(200);
				if (this.callResultContains(key)) {
					datas = this.removeCallResult(key);
				}
			}
		} catch (Exception ex) {
			log.error(ex.getMessage(), ex);
		}
		if (datas != null) {
			for (String data : datas) {
				String[] d = data.split(",");
				states.add(new LineState(1, Integer.valueOf(d[0]), d[1]));
			}
		}
		return states;
	}

	@Override
	public void processEvent(CTIEvent event) {
		log.info("engine event come : event source = {},event type = {}, event data = {}",
			event.getEventSource(), event.getEventType(), event.getEventData());
		switch (event.getEventSource()) {
			case TRUNK:
				switch (event.getEventType()) {
					case CALLIN:
						//来电事件
						eventHandler.callIncome(event);
						break;
					case ALERTCALL:
						//振铃事件
						eventHandler.alert(event);
						break;
					case ANSWERCALL:
						//摘机事件
						eventHandler.offHook(event);
						break;
					case CLEARCALL:
						//挂机事件
						eventHandler.onHook(event);
						break;
					case CALLOUT:
						//外呼结果事件
						eventHandler.callOut(event);
						if (event.getEventData().getState() != Constants.STATE_SUCCESS 
								&& isDeviceActive(event.getEventData().getDeviceID())) {
							log.warn("发生同抢！呼叫失败且通道被呼入占用，通道ID：{}", event.getEventData().getDeviceID());
							this.onHook(event.getEventData().getDeviceID() + "_" + event.getEventData().getLsh());
						}
						break;
					default:
						log.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
				}
				break;
			case VOC:
				switch (event.getEventType()) {
					case PLAY:
						//放音结束事件
						eventHandler.playEnd(event);
						break;
					case DTMF:
						//放音结束事件
						eventHandler.receiveDTMF(event);
						break;
					case RECORD:
						//放音结束事件
						eventHandler.recordEnd(event);
						break;
					default:
						log.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
				}
				break;
			case CONF:
				switch (event.getEventType()) {
					case JOINTOCONF:
						//加入会议事件
						eventHandler.joinToConf(event);
						break;
					case LEAVEFROMCONF:
						//离开会议事件
						eventHandler.leaveConf(event);
						break;
					case CLEARCONF:
						//会议结束事件
						eventHandler.clearConf(event);
						break;
					default:
						log.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
				}
				break;
			default:
				log.warn("unknow eventSource : {}", event.getEventSource());
		}
	}

    @Override
    public int broadCast(String callDeviceId, String fileName) {
        log.warn("暂未实现广播功能！");
        return 1;
    }

    @Override
    public int detectSpeech(String callDeviceId, String file, long timeout) {
        log.warn("暂未实现语音识别功能！");
        return 1;
    }

    @Override
    public int stopDetectSpeech(String callDeviceId) {
        log.warn("暂未实现语音识别功能！");
        return 1;
    }
}
