/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti.socket;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.impl.shcti.Configuration;
import java.net.InetSocketAddress;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.filter.executor.ExecutorFilter;
import org.apache.mina.transport.socket.nio.NioDatagramAcceptor;

/**
 *
 * <AUTHOR> 2015-11-27
 */
public class ShctiSocketServer {

	private static final Log log = LogFactory.getLog(ShctiSocketServer.class);

	private final CTIEngine engine;
	private final MessageProcesser mp;

	public ShctiSocketServer(CTIEngine engine) {
		this.engine = engine;
		this.mp = new MessageProcesser(engine);
	}

	/**
	 * The connector.
	 */
	private NioDatagramAcceptor acceptor = null;

	/**
	 * The is connected.
	 */
	public volatile boolean running;

	/**
	 * The terminated.
	 */
	public volatile boolean terminated = false;


	public MessageProcesser getMessageProcesser() {
		return mp;
	}
	/**
	 * Inits the connector.
	 *
	 * @return true, if successful
	 */
	public synchronized boolean initServer() {
		try {
			acceptor = new NioDatagramAcceptor();
//			acceptor.getFilterChain().addLast("logger", new LoggingFilter());
//			acceptor.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
			
			acceptor.setHandler(new ShctiServerHandler(this));// 设置事件处理器
			// 设置buffer的长度
			acceptor.getSessionConfig().setReadBufferSize(2048);
			// 设置连接超时时间
			acceptor.getSessionConfig().setIdleTime(IdleStatus.BOTH_IDLE, Integer.valueOf(Configuration.getProperty("shcti.heartbeat.seconds")) + 5);
			acceptor.getSessionConfig().setReadBufferSize(2048);
			acceptor.getSessionConfig().setReuseAddress(true);
			
			Executor threadPool = Executors.newCachedThreadPool();
			acceptor.getFilterChain().addLast("threadPool", new ExecutorFilter(threadPool));
			
			// 绑定端口
			acceptor.bind(new InetSocketAddress(24680));
			running = true;
		} catch (Exception e) {
			acceptor.dispose();
			running = false;
			log.error("在ShctiSocketServer中的initServer方法中出错原因是:" + e.getMessage());
			throw new IllegalStateException("ShctiSocketServer启动失败");
		}
		return running;
	}

	/**
	 * Interrupt.
	 */
	public synchronized void interrupt() {
		running = false;
		if (acceptor != null && !acceptor.isDisposed()) {
			acceptor.dispose();
		}
	}

	/**
	 * Close.
	 */
	public synchronized void close() {
		running = false;
		terminated = true;
		acceptor.dispose();
	}
}
