/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.engine.impl.xcc.nats;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.CTIEventProcess;
import com.sungoin.cti.engine.EngineState;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;
import com.sungoin.cti.engine.impl.xcc.XccEngine;
import com.sungoin.cti.engine.impl.xcc.XccEvent;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class MessageHandler implements Runnable {

    private static final Logger LOG = LoggerFactory.getLogger(MessageHandler.class);
    private static final BlockingQueue<String> QUEUE = new ArrayBlockingQueue<>(256);

    private final CTIEngine engine;
    private final JSONParser parser;

    public MessageHandler(CTIEngine engine) {
        this.engine = engine;
        this.parser = new JSONParser();
    }

    public void processMessage(String msg) {
        boolean result = QUEUE.offer(msg);
        if (!result) {
            LOG.error(" MessageQueue is full !");
        }
    }

    public void start() {
        new Thread(this).start();
    }

    @Override
    public void run() {
        LOG.info("MessageProcesser start running...");
        while (engine.getState() == EngineState.RUNNING) {
            try {
                String message = QUEUE.take();
                LOG.debug("开始处理消息：{}", message);
                JSONObject json = (JSONObject) parser.parse(message);
                String method = (String) json.get("method");
                JSONObject result = (JSONObject) json.get("result");
                if (method != null) {
                    //收到事件
                    Event event = null;
                    JSONObject params = (JSONObject) json.get("params");;
                    switch (method) {
                        case "Event.Channel":
                            event = new Event();
                            event.setUuid((String) params.get("uuid"));
                            event.setNodeUuid((String) params.get("node_uuid"));
                            event.setPeerUuid((String) params.get("peer_uuid"));
                            event.setState((String) params.get("state"));
                            event.setAnswered((Boolean) params.get("answered"));
                            event.setCidName((String) params.get("cid_name"));
                            event.setCidNumber((String) params.get("cid_number"));
                            event.setDestNumber((String) params.get("dest_number"));
                            event.setDirection((String) params.get("direction"));
//                            event.setBillsec((String) params.get("billsec"));
//                            event.setCause((String) params.get("cause"));
//                            event.setVideo((Boolean) params.get("video"));
                            break;
                        case "Event.DTMF":
                            event = new Event();
                            event.setUuid((String) params.get("uuid"));
                            event.setNodeUuid((String) params.get("node_uuid"));
                            event.setState("DTMF");
                            event.setDtmfDigit((String) params.get("dtmf_digit"));
                            event.setDtmfSource((String) params.get("dtmf_source"));
                            break;
                        case "Event.CDR":
                            JSONObject cdr = (JSONObject) params.get("cdr");
                            event = new Event();
                            event.setState("CDR");
                            event.setUuid((String) params.get("uuid"));
                            event.setNodeUuid((String) params.get("node_uuid"));
                            event.setRecordPath((String) cdr.get("x_recording_file"));
                            break;
                        default:
                            LOG.debug("received Other event: {}", method);
                            break;
                    }
                    if (event != null) {
                        handlerEvent(event);
                    }
                } else if (result != null) {
                    //收到通知
                    Response resp = new Response();
                    resp.setId((String) json.get("id"));
                    resp.setUuid((String) result.get("uuid"));
                    resp.setNodeUuid((String) result.get("node_uuid"));
                    resp.setCode((Long) result.get("code"));
                    resp.setMessage((String) result.get("message"));
                    resp.setCause((String) result.get("cause"));
                    resp.setData((JSONObject) result.get("data"));
                    handlerResponse(resp);
                }
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
            }
        }

    }

    private void handlerEvent(Event event) throws InterruptedException {
        EventSource source = null;
        EventType type = null;
        EventData edata = null;
        int lsh, state, stopCode, errorCode, reason;
        String deviceId = event.getUuid();
        switch (event.getState()) {
            case "START":
                LOG.info("来电事件。。。deviceId：{}", deviceId);
                lsh = ((XccEngine) this.engine).getNextLsh();
                ((XccEngine) this.engine).putDeviceLsh(deviceId, lsh);
                ((XccEngine) this.engine).putNodeId(deviceId, event.getNodeUuid());
                edata = new EventData(deviceId, lsh, null);
                source = EventSource.TRUNK;
                type = EventType.CALLIN;
                edata.setCaller(event.getCidNumber());
                edata.setCallee(event.getDestNumber());
                edata.setModule("0");
                edata.setChannel("0");
                break;
            case "DTMF":
                String dtmf = event.getDtmfDigit();
                LOG.info("DTFM事件，deviceId：{}，收到的按键：{}", deviceId, dtmf);
                lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
                if (lsh < 0) {
                    return;
                }
                LOG.debug("deviceId:{},lsh:{}", deviceId, lsh);
                source = EventSource.VOC;
                edata = new EventData(deviceId, lsh, null);
                type = EventType.DTMF;
                edata.setState(1);
                edata.setDtmf(dtmf);
                break;
            case "CDR":
                LOG.info("CDR事件，deviceId：{}，Record path: {}", deviceId, event.getRecordPath());
                if(StringUtils.isEmpty(event.getRecordPath())) {
                    return;
                }
                lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
                if (lsh < 0) {
                    return;
                }
                source = EventSource.TRUNK;
                type = EventType.CDR;
                edata = new EventData(deviceId, lsh, null);
                edata.setRecord(event.getRecordPath());
                break;
            case "DESTROY":
                lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
                LOG.info("挂机事件, uuid:{}， lsh：{}", deviceId, lsh);
                if (lsh < 0) {
                    return;
                }
                if (event.getDirection().equals("outbound") && !event.isAnswerd()) {
                    //外呼未呼通，不发送挂机事件
                    return;
                }
                source = EventSource.TRUNK;
                type = EventType.CLEARCALL;
                edata = new EventData(deviceId, lsh, null);
                edata.setState(1);
                edata.setErrorCode(0);
                ((XccEngine) this.engine).clearCall(deviceId);
                break;
            default:
                break;
        }
        if (edata != null) {
            ((CTIEventProcess) engine).processEvent(new XccEvent(source, type, edata));
        }
    }

    private void handlerResponse(Response resp) throws InterruptedException {
        EventSource source = null;
        EventType type = null;
        EventData edata = null;
        int lsh, state, stopCode, errorCode, reason;
        String deviceId = resp.getUuid();
        String id = resp.getId();
        long code = resp.getCode();
        //外呼结果特殊处理
        if (id.startsWith("DIAL")) {
            deviceId = id.substring(5);
            lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
            LOG.debug("dial response...deviceId:{},lsh:{}", deviceId, lsh);
            if(code == 200) {
                ((XccEngine) this.engine).putNodeId(deviceId, resp.getNodeUuid());
            }
            source = EventSource.TRUNK;
            type = EventType.CALLOUT;
            edata = new EventData(deviceId, lsh, null);
            edata.setState(code == 200 ? 1 : 0);
            edata.setErrorCode(0);
        } else {
            switch (id) {
                case "ACCEPT":
                    lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
                    LOG.debug("alert response...deviceId:{},lsh:{}", deviceId, lsh);
                    edata = new EventData(deviceId, lsh, null);
                    source = EventSource.TRUNK;
                    type = EventType.ALERTCALL;
                    edata.setState(code == 200 ? 1 : 0);
                    edata.setErrorCode(0);
                    break;
                case "ANSWER":
                    lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
                    LOG.debug("answer response...deviceId:{},lsh:{}", deviceId, lsh);
                    edata = new EventData(deviceId, lsh, null);
                    source = EventSource.TRUNK;
                    type = EventType.ANSWERCALL;
                    edata.setState(code == 200 ? 1 : 0);
                    edata.setErrorCode(0);
                    break;
                case "PLAY":
                    lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
                    LOG.debug("play response...deviceId:{},lsh:{}", deviceId, lsh);
                    if (lsh < 0) {
                        return;
                    }
                    edata = new EventData(deviceId, lsh, null);
                    source = EventSource.VOC;
                    type = EventType.PLAY;
                    edata.setState(code == 200 ? 1 : 0);
                    edata.setErrorCode(0);
                    break;
                case "STOP":
                    //只有nodeUuid,没有Uuid
                    LOG.debug("收到STOP事件反馈不处理，code={}", code);
                    break;
                case "CHANNEL-BRIDGE":
                    //只有nodeUuid,没有Uuid
                    LOG.debug("收到CHANNEL-BRIDGE事件反馈不处理，code={}", code);
                    break;
                case "BROADCAST":
                    LOG.debug("收到BROADCAST事件反馈不处理，code={}", code);
                    break;
                case "DETECTSPEECH":
                    LOG.debug("语音识别结果：code={}", code);
                    if(resp.getData() != null && "Speech.End".equals(resp.getData().get("type"))) {
                        String text = (String) resp.getData().get("text");
                        LOG.debug("语音识别内容为：{}", text);
                        lsh = ((XccEngine) this.engine).getDeviceLsh(deviceId);
                        edata = new EventData(deviceId, lsh, null);
                        source = EventSource.VOC;
                        type = EventType.SPEECH;
                        edata.setText(text);
                        edata.setState(code == 200 ? 1 : 0);
                        edata.setErrorCode(0);
                    }
                    break;
                default:
                    LOG.warn("忽略处理的事件类型：{}，code={}", resp.getId(), code);
                    return;
            }
        }
        if (edata != null) {
            ((CTIEventProcess) engine).processEvent(new XccEvent(source, type, edata));
        }
    }

}
