/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.djkeygoe;

import com.sungoin.cti.engine.impl.djkeygoe.local.Global;
import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.CTIEvent;
import com.sungoin.cti.engine.ConferenceMode;
import com.sungoin.cti.engine.DeviceStatistic;
import com.sungoin.cti.engine.DeviceType;
import com.sungoin.cti.engine.EngineState;
import com.sungoin.cti.engine.MakeCallType;
import com.sungoin.cti.engine.exception.ConferenceFullException;
import com.sungoin.cti.engine.exception.IllegalFilePathExcaption;
import com.sungoin.cti.engine.exception.TrunkDeviceFullException;
import com.sungoin.cti.engine.exception.VoiceDeviceFullException;
import com.sungoin.cti.engine.CTIEventHandler;
import com.sungoin.cti.engine.LineState;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-5-4
 */
public class DJKeygoeEngine implements CTIEngine {

	private static final Logger log = LoggerFactory.getLogger(DJKeygoeEngine.class);

	private CTIEventHandler eventHandler;
	private EngineState state = EngineState.STOPED;

	private final Global global = new Global(this);

	@Override
	public EngineState getState() {
		return state;
	}

	@Override
	public void start() {
		global.start();
		state = EngineState.STARTED;
		log.info("cti engine start successful...");
	}

	@Override
	public void init(CTIEventHandler eventHandler) {
		global.init();
		state = EngineState.RUNNING;
		this.eventHandler = eventHandler;
		log.info("cti enging init successful...");
	}

	@Override
	public void shotdown() {
		global.shutdown();
		state = EngineState.STOPED;
		log.info("cti enging shutdown successful...");
	}

	@Override
	public int alert(String callDeviceId) throws VoiceDeviceFullException {
		log.debug("do alert method deviceId:{}", callDeviceId);
		return global.alertCallin(callDeviceId);
	}

	@Override
	public int answer(String callDeviceId) throws VoiceDeviceFullException {
		log.debug("do answer method deviceId:{}", callDeviceId);
		return global.answerCallin(callDeviceId);
	}

	@Override
	public int play(String callDeviceId, String fileName, boolean loop, boolean isQueue, int maxSecond) throws IllegalFilePathExcaption {
		log.debug("do play method deviceId:{},fileName:{},loop:{},isQueue:{},maxSecond:{}", callDeviceId, fileName, loop, isQueue, maxSecond);
		return global.playFile(callDeviceId, fileName, 0, loop, isQueue, maxSecond);
	}

	@Override
	public int onHook(String callDeviceId) {
		log.debug("do onHook method deviceId:{}", callDeviceId);
		return global.onHook(callDeviceId);
	}

	@Override
	public int stopPlay(String callDeviceId) {
		log.debug("do stopPlay method deviceId:{}", callDeviceId);
		return global.stopPlay(callDeviceId);
	}

	@Override
	public int record(String callDeviceId, String fileName, int length, boolean isAppend) throws IllegalFilePathExcaption {
		log.debug("do record method deviceId:{},fileName:{},length:{},isAppend:{}", callDeviceId, fileName, length, isAppend);
		int fixed = length < 0 ? 0 : length;
		return global.record(callDeviceId, fileName, fixed, isAppend);
//        return global.recordCsp(callDeviceId, fileName);
	}

	@Override
	public int stopRecord(String callDeviceId) {
		log.debug("do stopRecord method deviceId:{}", callDeviceId);
		return global.stopRecord(callDeviceId);
	}

	@Override
	public String createAndJoinConf(String callDeviceId, ConferenceMode mode) throws VoiceDeviceFullException, ConferenceFullException {
		log.debug("do createAndJoinConf method deviceId:{},mode:{}", callDeviceId, mode);
		return global.joinToConf(callDeviceId, mode.ordinal(), null);
	}

	@Override
	public String joinConf(String callDeviceId, ConferenceMode mode, String confDeviceID) throws VoiceDeviceFullException, ConferenceFullException {
		log.debug("do JoinConf method deviceId:{},mode:{},confDeviceID:{}", callDeviceId, mode, confDeviceID);
		return global.joinToConf(callDeviceId, mode.ordinal(), confDeviceID);
	}

	@Override
	public String[] makeCall(MakeCallType type, String caller, String callee) throws TrunkDeviceFullException, VoiceDeviceFullException {
		return makeCall(type, caller, callee, null, null);
	}
	
	@Override
	public String[] makeCall(MakeCallType type, String caller, String callee, String origCallee, String redirectNo) throws TrunkDeviceFullException, VoiceDeviceFullException {
		log.debug("do makeCall method type:{},caller:{},callee:{},origCallee:{}", type, caller, callee, origCallee);
		if (type == MakeCallType.INLINE) {
			return global.makeCallinLine(caller, callee);
		} else {
			return global.makeCalloutLine(caller, callee, origCallee, redirectNo);
		}
	}

	@Override
	public int connectCall(String callerDeviceID, String calleeDeviceID) {
		log.debug("do connectCall method callerDeviceID:{},calleeDeviceID:{}", callerDeviceID, calleeDeviceID);
		return connectCall(callerDeviceID, calleeDeviceID, true);
	}
    
    public int connectCall(String callerDeviceID, String calleeDeviceID, boolean sameDsp) {
        log.debug("do connectCall method callerDeviceID:{},calleeDeviceID:{}, sameDsp:{}", callerDeviceID, calleeDeviceID, sameDsp);
        return global.connectCall(callerDeviceID, calleeDeviceID, sameDsp);
    }

	@Override
	public int disConnectCall(String callerDeviceID, String calleeDeviceID) {
		log.debug("do disConnectCall method callerDeviceID:{},calleeDeviceID:{}", callerDeviceID, calleeDeviceID);
		return global.disConnectCall(callerDeviceID, calleeDeviceID);
	}

	@Override
	public void processEvent(CTIEvent event) {
		log.info("engine event come : event source = {},event type = {}, event data = {}",
			event.getEventSource(), event.getEventType(), event.getEventData());
		switch (event.getEventSource()) {
			case TRUNK:
				switch (event.getEventType()) {
					case CALLIN:
						//来电事件
						eventHandler.callIncome(event);
						break;
					case ALERTCALL:
						//振铃事件
						eventHandler.alert(event);
						break;
					case ANSWERCALL:
						//摘机事件
						eventHandler.offHook(event);
						break;
					case CLEARCALL:
						//挂机事件
						eventHandler.onHook(event);
						break;
					case CALLOUT:
						//外呼结果事件
						eventHandler.callOut(event);
						break;
					default:
						log.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
				}
				break;
			case VOC:
				switch (event.getEventType()) {
					case PLAY:
						//放音结束事件
						eventHandler.playEnd(event);
						break;
					case DTMF:
						//放音结束事件
						eventHandler.receiveDTMF(event);
						break;
					case RECORD:
						//放音结束事件
						eventHandler.recordEnd(event);
						break;
					default:
						log.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
				}
				break;
			case CONF:
				switch (event.getEventType()) {
					case JOINTOCONF:
						//加入会议事件
						eventHandler.joinToConf(event);
						break;
					case LEAVEFROMCONF:
						//离开会议事件
						eventHandler.leaveConf(event);
						break;
					case CLEARCONF:
						//会议结束事件
						eventHandler.clearConf(event);
						break;
					default:
						log.warn("in eventSource: {} unknow eventType : {}", event.getEventSource(), event.getEventType());
				}
				break;
			default:
				log.warn("unknow eventSource : {}", event.getEventSource());
		}
	}

	@Override
	public DeviceStatistic getDeviceStatistic(DeviceType dt) {
		log.debug("do getDeviceStatistic method DeviceType:{}", dt);
		return global.getStatistic(dt);
	}

	@Override
	public List<DeviceStatistic> getAllDeviceStatistic() {
		log.debug("do getDeviceStatistic method");
		return global.getAllDeviceStatistic();
	}

	@Override
	public int getCalleeCallNum(String callee) {
		return global.getCalleeCallNum(callee);
	}

	@Override
	public void resetDevice(DeviceType devType, String deviceId) {
           global.resetDevice(devType, deviceId);
	}

	@Override
	public int playList(String callDeviceId, String[] fileNames) throws IllegalFilePathExcaption {
		return global.playList(callDeviceId, fileNames, 0);
	}

	@Override
	public int leaveConf(String callDeviceId) {
		log.debug("do leaveConf method deviceId:{}", callDeviceId);
		return global.leaveConf(callDeviceId);
	}

	@Override
	public int getLicenseDays() {
		log.debug("do getLicenseDays method");
		return global.getLicenseDays();
	}

	@Override
	public int playDtmf(String callDeviceId, String dtmfStr) {
		log.debug("do playDtmf method deviceId:{},dtmfStr={}", callDeviceId, dtmfStr);
		return global.playDtmf(callDeviceId, dtmfStr);
	}

	@Override
	public int playTone(String callDeviceId, int nPlayType) {
		log.debug("do playTone method deviceId:{},nPlayType={}", callDeviceId, nPlayType);
		return global.playTone(callDeviceId, nPlayType);
	}

	@Override
	public List<LineState> getTrunkLineState() {
		return global.getTrunkLineInfos();
	}

    @Override
    public int broadCast(String callDeviceId, String fileName) {
        log.warn("暂未实现广播功能！");
        return 1;
    }

    @Override
    public int detectSpeech(String callDeviceId, String file, long timeout) {
        log.warn("暂未实现语音识别功能！");
        return 1;
    }

    @Override
    public int stopDetectSpeech(String callDeviceId) {
        log.warn("暂未实现语音识别功能！");
        return 1;
    }
	
}
