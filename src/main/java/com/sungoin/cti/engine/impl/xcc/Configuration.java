/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.engine.impl.xcc;

import com.sungoin.cti.engine.exception.InitEngineFailException;
import java.io.IOException;
import java.util.Properties;

/**
 *
 * <AUTHOR>
 */
public class Configuration {
    private static final Properties prop = new Properties();
    public static boolean CALLER_KEEP_PREFIX = false;

	private Configuration() {
	}
	
	static {
		try {
			prop.load(Configuration.class.getClassLoader().getResourceAsStream(Constants.PROP_PATH));
            if(prop.containsKey(Constants.CALLER_KEEP_PREFIX)) {
                CALLER_KEEP_PREFIX = Boolean.valueOf(prop.getProperty(Constants.CALLER_KEEP_PREFIX));
            }
		} catch (IOException ex) {
			throw new InitEngineFailException("can not load xcc.properties!", ex);
		}
	}
	
	public static String getProperty(String key) {
		return prop.getProperty(key);
	}
	
	public static String getProperty(String key, String defaultValue) {
		return prop.getProperty(key, defaultValue);
	}
    
    public static String getNatsUrl() {
        return prop.getProperty(Constants.NATS_URL);
    }
    
    public static String getNatsTopic() {
        return prop.getProperty(Constants.NATS_TOPIC);
    }
    
    public static String getGatewayAddress() {
        return prop.getProperty(Constants.GATEWAY_ADDRESS);
    }
    
    public static String getRedirectPrefix() {
        return prop.getProperty(Constants.REDIRECT_PREFIX);
    }
    
    public static String getRedirectSuffix() {
        return prop.getProperty(Constants.REDIRECT_SUFFIX);
    }
    
    public static String getRedirectResson() {
        return prop.getProperty(Constants.REDIRECT_REASON);
    }
    
    public static String getRedirectCounter() {
        return prop.getProperty(Constants.REDIRECT_COUNTER);
    }
    
    public static String getRedirectPrivacy() {
        return prop.getProperty(Constants.REDIRECT_PRIVACY);
    }
    
    public static String getReplacePath() {
        return prop.getProperty(Constants.REPLACE_PATH);
    }
    
    public static String getSipGatewayPrefix() {
        return prop.getProperty(Constants.SIP_GATEWAY_PREFIX);
    }
    
    public static String getZeroGatewayPrefix() {
        return prop.getProperty(Constants.ZERO_GATEWAY_PREFIX);
    }
    
    public static String getAutoNotifyGatewayPrefix() {
        return prop.getProperty(Constants.AUTO_NOTIFY_GATEWAY_PREFIX);
    }
    public static String getAutoTestGatewayPrefix() {
        return prop.getProperty(Constants.AUTO_TEST_GATEWAY_PREFIX);
    }
}
