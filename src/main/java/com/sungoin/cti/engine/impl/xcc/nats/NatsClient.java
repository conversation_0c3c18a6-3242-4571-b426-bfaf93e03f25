/*
 * Click nbfs://nbhost/SystemFileSystem/Templates/Licenses/license-default.txt to change this license
 * Click nbfs://nbhost/SystemFileSystem/Templates/Classes/Class.java to edit this template
 */
package com.sungoin.cti.engine.impl.xcc.nats;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.impl.xcc.Configuration;
import com.sungoin.cti.engine.impl.xcc.Constants;
import io.nats.client.Connection;
import io.nats.client.Dispatcher;
import io.nats.client.Nats;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.UUID;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class NatsClient {
    private static final Logger LOG = LoggerFactory.getLogger(NatsClient.class);
    private static final String CONTROLLER_UUID = "sungoin";
    private static final String SUBJECT_PREFIX = "cn.xswitch.";
    
    private final CTIEngine engine;
    private final MessageHandler handler;
    
    private Connection nc;
    
    public NatsClient(CTIEngine engine) {
        this.engine = engine;
        this.handler = new MessageHandler(engine);
    }
    
    public void close() {
        try {
            nc.close();
        } catch (InterruptedException ex) {
            LOG.error(ex.getMessage(), ex);
        }
    }
    
    public void start() throws IOException, InterruptedException {
        nc = Nats.connect(Configuration.getNatsUrl());
        Dispatcher d = nc.createDispatcher();
        //订阅消息
        d.subscribe(Configuration.getNatsTopic(), (msg) ->{
            String message = new String(msg.getData(), StandardCharsets.UTF_8);
//            LOG.debug("收到消息：{}", message);
            this.handler.processMessage(message);
        });
        handler.start();
    }
    
    //应答
    public void accept(String uuid, String nodeId) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        request.NatsRequest("ACCEPT", SUBJECT_PREFIX + "node." + nodeId, "Xnode.Accept", rpc, nc);
    }
    
    //应答
    public void answer(String uuid, String nodeId) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        request.NatsRequest("ANSWER", SUBJECT_PREFIX + "node." + nodeId, "Xnode.Answer", rpc, nc);
    }
    
    public void play(String uuid, String nodeId, String file) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        JSONObject media = new JSONObject();
        media.put("type", "FILE");
        media.put("data", file);
        rpc.put("media", media);
        request.NatsRequest("PLAY", SUBJECT_PREFIX + "node." + nodeId, "Xnode.Play", rpc, nc);
    }
    
    //停止放音
    public void stop(String uuid, String nodeId) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        request.NatsRequest("STOP", SUBJECT_PREFIX + "node." + nodeId, "Xnode.Stop", rpc, nc);
    }
    
    //广播
    public void broadcast(String uuid, String nodeId, String file) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        rpc.put("option", "BOTH");
        JSONObject media = new JSONObject();
        media.put("type", "FILE");
        media.put("data", file);
        rpc.put("media", media);
        request.NatsRequest("BROADCAST", SUBJECT_PREFIX + "node." + nodeId, "Xnode.Broadcast", rpc, nc);
    }
    
    //外呼
    public void dial(String uuid, String caller, String callee, String originalNo) {
        if(callee.substring(0, 1).equals(Constants.SIP_PHONE_PREFIX)) {
            String[] ss = callee.split("\\+");
            if(ss.length == 2) {
                callee = ss[0];
                originalNo = ss[1];
            }
        }
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        JSONObject gloabParams = new JSONObject();
        gloabParams.put("ignore_early_media", "true");
        JSONObject destination = new JSONObject();
        JSONObject callParam = new JSONObject();
        callParam.put("uuid",  uuid);//新channel的uuid，ctrl生成
        callParam.put("cid_number",  caller);
        callParam.put("dial_string", getDialString(callee, originalNo));
        JSONObject params = new JSONObject();
        params.put("ignore_early_media", "true");
        params.put("origination_caller_id_number", caller);
        if(!callee.startsWith(Constants.SIP_PHONE_PREFIX) && originalNo != null) {
            params.put("sip_h_Diversion", this.getDiversionString(originalNo));
        }
        callParam.put("params", params);
        JSONArray callArray = new JSONArray();
        callArray.add(callParam);
        destination.put("call_params", callArray);
        destination.put("global_params", gloabParams);
        rpc.put("destination", destination);
        request.NatsRequest("DIAL#" + uuid, SUBJECT_PREFIX + "node.test", "Xnode.Dial", rpc, nc);
    }
    
    private String getDialString(String callee, String originalNo) {
        String prefix = callee.substring(0,  1);
        switch (prefix) {
            case Constants.SIP_PHONE_PREFIX:
                return Configuration.getSipGatewayPrefix() + callee + "/" + originalNo;
            case Constants.ZERO_PHONE_PREFIX:
                return Configuration.getZeroGatewayPrefix() + callee.substring(1);
            case Constants.AUTO_NOTIFY_PHONE_PREFIX:
                return Configuration.getAutoNotifyGatewayPrefix() + callee.substring(1);
            case Constants.AUTO_TEST_PHONE_PREFIX:
                return Configuration.getAutoTestGatewayPrefix() + callee.substring(1);
            default:
                return Configuration.getGatewayAddress() + callee;
        }
    }
    
    //桥接
    public void bridge(String uuid, String uuid2) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        rpc.put("peer_uuid", uuid2);
        request.NatsRequest("CHANNEL-BRIDGE", SUBJECT_PREFIX + "node.test", "Xnode.ChannelBridge2", rpc, nc);
    }
    
    //解除桥接
    public void unBridge(String uuid, String uuid2) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        rpc.put("peer_uuid", uuid2);
        request.NatsRequest("UNBRIDGE", SUBJECT_PREFIX + "node.test", "Xnode.UnBridge", rpc, nc);
    }
    
    //挂机
    public void hangup(String uuid) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        request.NatsRequest("HANGUP", SUBJECT_PREFIX + "node.test", "Xnode.Hangup", rpc, nc);
    }
    
    private String getDiversionString(String redirectNo) {
        String prefix = Configuration.getRedirectPrefix();
        String suffix = Configuration.getRedirectSuffix();
        String reason = Configuration.getRedirectResson();
        String counter = Configuration.getRedirectCounter();
        String privacy = Configuration.getRedirectPrivacy();
        StringBuilder diversion = new StringBuilder();
        diversion.append(prefix).append(redirectNo).append(suffix).append(";reason=").append(reason).append(";counter=").append(counter)
                .append(";privacy=").append(privacy).append("\r\n");
        return diversion.toString();
    }
    
    //录音
    public void record(String uuid, String nodeId, String file) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        rpc.put("path", file);
        rpc.put("action", "START");
        request.NatsRequest("RECORD", SUBJECT_PREFIX + "node." + nodeId, "Xnode.Record", rpc, nc);
    }
    
    //语音识别
    public void detectSpeech(String uuid, String nodeId, String file, long timeout) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        //speech
        JSONObject speech = new JSONObject();
        speech.put("engine", "ali");
        speech.put("nobreak", false);
        speech.put("no_input_timeout", 1000);
        speech.put("speech_timeout", timeout);
        speech.put("partial_event", false);
        speech.put("disable_detected_data_event", true);
        rpc.put("speech", speech);
        //media
        if(file != null) {
            JSONObject media = new JSONObject();
            media.put("type", "FILE");
            media.put("data", file);
            rpc.put("media", media);
        }
        JSONObject dtmf = new JSONObject();
        dtmf.put("max_digits", 1);
        dtmf.put("ctrl_uuid", CONTROLLER_UUID);
        dtmf.put("uuid", uuid);
        dtmf.put("timeout", timeout);
        dtmf.put("digit_timeout", 2000);
        rpc.put("dtmf", dtmf);
        request.NatsRequest("DETECTSPEECH", SUBJECT_PREFIX + "node." + nodeId, "Xnode.DetectSpeech", rpc, nc);
    }
    
    public void stopDetectSpeech(String uuid, String nodeId) {
        Request request = new Request();
        JSONObject rpc = new JSONObject();
        rpc.put("ctrl_uuid", CONTROLLER_UUID);
        rpc.put("uuid", uuid);
        request.NatsRequest("STOPDETECTSPEECH", SUBJECT_PREFIX + "node." + nodeId, "Xnode.StopDetectSpeech", rpc, nc);
    }
}
