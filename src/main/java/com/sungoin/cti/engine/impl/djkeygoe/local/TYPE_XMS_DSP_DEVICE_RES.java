/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.djkeygoe.local;

import DJKeygoe.DeviceID_t;
import java.util.concurrent.atomic.AtomicInteger;

/**
 *
 * <AUTHOR> 2015-5-4
 */
public class TYPE_XMS_DSP_DEVICE_RES {

	
	public static final int MAX_VOC_RES_NUM_EACH_DSP = Integer.parseInt(Configuration.getProperty("MAX_VOC_RES_NUM_EACH_DSP"));
	public static final int MAX_PCM_RES_NUM_EACH_DSP = Integer.parseInt(Configuration.getProperty("MAX_PCM_RES_NUM_EACH_DSP"));
	public static final int MAX_TRUNK_RES_NUM_EACH_DSP = Integer.parseInt(Configuration.getProperty("MAX_TRUNK_RES_NUM_EACH_DSP"));
	public static final int MAX_MEMBER_PER_CONF_GROUP = Integer.parseInt(Configuration.getProperty("MAX_CONF_RES_NUM_EACH_DSP"));

	public String uuid;
	public long lFlag = 0;

	public DeviceID_t deviceID = new DeviceID_t();
	public int iSeqID = 0;
	public boolean bOpenFlag = false;
	public boolean bErrFlag = false;
	public int RemoveState = -1;

	public AtomicInteger lVocNum = new AtomicInteger();
	public AtomicInteger lVocOpened = new AtomicInteger();
	public AtomicInteger lVocFreeNum = new AtomicInteger();
	public VOICE_STRUCT[] pVoice = new VOICE_STRUCT[MAX_VOC_RES_NUM_EACH_DSP];

	public AtomicInteger lPcmNum = new AtomicInteger();
	public AtomicInteger lPcmOpened = new AtomicInteger();
	public PCM_STRUCT[] pPcm = new PCM_STRUCT[MAX_PCM_RES_NUM_EACH_DSP];

	public AtomicInteger lTrunkNum = new AtomicInteger();
	public AtomicInteger lTrunkOpened = new AtomicInteger();
	public TRUNK_STRUCT[] pTrunk = new TRUNK_STRUCT[MAX_TRUNK_RES_NUM_EACH_DSP];

	public AtomicInteger lConfNum = new AtomicInteger();			// the XMS_DEVMAIN_CONFERENCE number in this DSP
	public AtomicInteger lConfOpened = new AtomicInteger();		// the Conf number opened by OpenDevice()
	public AtomicInteger lConfFreeNum = new AtomicInteger();		// the free Conf number in this DSP
	public CONF_STRUCT[] pConf = new CONF_STRUCT[MAX_MEMBER_PER_CONF_GROUP];
}
