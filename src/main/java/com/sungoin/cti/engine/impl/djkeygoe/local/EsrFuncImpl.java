package com.sungoin.cti.engine.impl.djkeygoe.local;

import DJ<PERSON>eygoe.DJAcsAPIDef;
import DJ<PERSON><PERSON>goe.Acs_Evt_t;
import DJKeygoe.DEVICE_CALL_STATE;
import DJKeygoe.DJEvtDeviceStateData;
import DJKeygoe.DJEvtLicenseQueryData;
import DJKeygoe.DJEvtQueryDeviceData;
import DJKeygoe.DeviceID_t;
import DJKeygoe.EsrFunc;
import DJKeygoe.XMS_ANALOGTRUNK_PARAM_TYPE;
import DJKeygoe.XMS_DEVMAIN_TYPE;
import DJKeygoe.XMS_EVT_TYPE;
import DJKeygoe.XMS_INTERFACE_DEVSUB_TYPE;
import com.sungoin.cti.boot.CTIApplication;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;
import com.sungoin.cti.server.config.CommonSetting;
import com.sungoin.cti.server.service.mq.MqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class EsrFuncImpl implements EsrFunc {

	public static final Logger log = LoggerFactory.getLogger(EsrFuncImpl.class);

	private final Global GlobalVar;

	public EsrFuncImpl(Global GlobalVar) {
		this.GlobalVar = GlobalVar;
	}

	@Override
	public void UserProcess(Acs_Evt_t acsEvtData, Object obj) {
		int nEvtType = acsEvtData.m_s32EventType;
		DeviceID_t device = acsEvtData.m_DeviceID;
		log.debug("event come... event type is : {}, Dev = {},obj={}", GetString.GetString_EvtType(nEvtType),
			"(" + GetString.GetString_DeviceMain(device.m_s16DeviceMain) + "," + GetString.GetString_DeviceSub(device.m_s16DeviceSub) + ")", obj);
		switch (nEvtType) {
			case XMS_EVT_TYPE.XMS_EVT_LICENSE_QUERY: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				DJEvtLicenseQueryData ascLicenseQueryData = (DJEvtLicenseQueryData) obj;
				GlobalVar.licenseDays = ascLicenseQueryData.acsLicenseQueryData.m_u16LicLeftDay;
				GlobalVar.licenseQueryTime = System.currentTimeMillis();
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_OPEN_STREAM: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				break;
			}
			//获取设备信息或当设备信息发生改变时调用
			case XMS_EVT_TYPE.XMS_EVT_QUERY_ONE_DSP_START: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				break;
			}
			//获取设备信息调用成功，返回DSP类型设备列表信息
			case XMS_EVT_TYPE.XMS_EVT_QUERY_DEVICE: {

				GlobalVar.DispEventInfo(device, obj, nEvtType);

				DJEvtQueryDeviceData acsQueryDevice = (DJEvtQueryDeviceData) obj;
				log.debug("partWork is {}, ModuleId is {}", GlobalVar.cfg_iPartWork, GlobalVar.cfg_iPartWorkModuleID);
				//是否指定DSP模块并且是指定的模块ID
				if (GlobalVar.cfg_iPartWork == 0
					|| device.m_s8ModuleID == GlobalVar.cfg_iPartWorkModuleID) {
					log.debug("32DeviceMain is {}", acsQueryDevice.acsDevListHead.m_s32DeviceMain);
					switch (acsQueryDevice.acsDevListHead.m_s32DeviceMain) {
						//DSP 板卡设备  {8}
						case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_BOARD: {
							GlobalVar.AddDeviceRes_Board(acsQueryDevice);
							break;
						}
						// Ch接口设备，用于取出DSP 的ACS_DEVMAIN_INTERFACE_CH 数量  {5}
						case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH: {
							GlobalVar.AddDeviceRes_Trunk(acsQueryDevice);
							break;
						}
						//语音设备 {2}
						case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_VOICE: {
							GlobalVar.AddDeviceRes_Voice(acsQueryDevice);
							break;
						}
						//数字端口驱动 {4}
						case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_DIGITAL_PORT: {
							GlobalVar.AddDeviceRes_Pcm(acsQueryDevice);
							break;
						}
						//会议组设备
						case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_CONFERENCE: {
							log.debug("会议组设备！");
							GlobalVar.AddDeviceRes_Conf(acsQueryDevice);
							break;
						}

						default: {
							break;
						}
					}
				}
				break;
			}
			// Keygoe服务器结束 结束了返回DSP 设备列表事件
			case XMS_EVT_TYPE.XMS_EVT_QUERY_ONE_DSP_END: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				if (GlobalVar.cfg_iPartWork == 0
					|| device.m_s8ModuleID == GlobalVar.cfg_iPartWorkModuleID) {
					//将设备标示修改为可用
					GlobalVar.AllDeviceRes[device.m_s8ModuleID].lFlag = 1;
					GlobalVar.OpenAllDevice_Dsp(device.m_s8ModuleID);
					GlobalVar.RefreshMapTable();
					log.info(device.m_s8ModuleID + "");
				}
				break;
			}

			case XMS_EVT_TYPE.XMS_EVT_QUERY_REMOVE_ONE_DSP_END: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				GlobalVar.AllDeviceRes[device.m_s8ModuleID].lFlag = 0;		// Remove DSP Over
				GlobalVar.RefreshMapTable();
				break;
			}

			case XMS_EVT_TYPE.XMS_EVT_QUERY_DEVICE_END: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				break;
			}
			case XMS_EVT_TYPE.XMS_EVT_OPEN_DEVICE: { //Open Device OK!
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				byte bDspModuleID = device.m_s8ModuleID;
				short sChannelID = device.m_s16ChannelID;
				switch (device.m_s16DeviceMain) {
					case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_BOARD: {
						GlobalVar.AllDeviceRes[bDspModuleID].deviceID.m_CallID.m_s32FlowChannel
							= device.m_CallID.m_s32FlowChannel;
						GlobalVar.AllDeviceRes[bDspModuleID].deviceID.m_CallID.m_s32FlowType
							= device.m_CallID.m_s32FlowType;
						GlobalVar.SetGTD_ToneParam(GlobalVar.AllDeviceRes[bDspModuleID].deviceID);
						break;
					}

					case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH: {
						TRUNK_STRUCT OneTrk = GlobalVar.GetTrunkStructByDevice(bDspModuleID, sChannelID);
						OneTrk.uuid = "trunk-" + bDspModuleID + "-" + sChannelID;
						OneTrk.deviceID.m_CallID.m_s32FlowChannel = device.m_CallID.m_s32FlowChannel;
						OneTrk.deviceID.m_CallID.m_s32FlowType = device.m_CallID.m_s32FlowType;
						GlobalVar.InitTrunkChannel(OneTrk);
						GlobalVar.initTrunkToMap(OneTrk);

						DJAcsAPIDef.getInstance().XMS_ctsResetDevice(GlobalVar.g_acsHandle, OneTrk.deviceID, null);
						DJAcsAPIDef.getInstance().XMS_ctsGetDevState(GlobalVar.g_acsHandle, OneTrk.deviceID, null);

						// modify the count
						GlobalVar.g_iTotalTrunkOpened.incrementAndGet();
						GlobalVar.AllDeviceRes[OneTrk.deviceID.m_s8ModuleID].lTrunkOpened.incrementAndGet();

						if (OneTrk.deviceID.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH
							&& OneTrk.deviceID.m_s16DeviceSub == XMS_INTERFACE_DEVSUB_TYPE.XMS_DEVSUB_ANALOG_TRUNK) {

							short u16ParamType = XMS_ANALOGTRUNK_PARAM_TYPE.ANALOGTRUNK_PARAM_UNIPARAM;

							GlobalVar.g_AnalogTrk.m_u16CallInRingCount = 3;
							GlobalVar.g_AnalogTrk.m_u16CallInRingTimeOut = 6000;

							int nRet = DJAcsAPIDef.getInstance().XMS_ctsSetParam(GlobalVar.g_acsHandle, OneTrk.deviceID, u16ParamType, (short) 0, GlobalVar.g_AnalogTrk);
                            // Search Free Voice for get CallerID(FSK)
							// if you needn't CallerID, ignore next line
							// GlobalVar.PrepareForCallerID(OneTrk); //
						}

						break;
					}

					case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_VOICE: {
						VOICE_STRUCT OneVoc = GlobalVar.GetVoiceStructByDevice(bDspModuleID, sChannelID);
						OneVoc.uuid = "voice-" + bDspModuleID + "-" + sChannelID;
						OneVoc.deviceID.m_CallID.m_s32FlowChannel = device.m_CallID.m_s32FlowChannel;
						OneVoc.deviceID.m_CallID.m_s32FlowType = device.m_CallID.m_s32FlowType;

						GlobalVar.Change_Voc_State(OneVoc, VOICE_STATE.VOC_FREE);

						DJAcsAPIDef.getInstance().XMS_ctsResetDevice(GlobalVar.g_acsHandle, OneVoc.deviceID, null);
						DJAcsAPIDef.getInstance().XMS_ctsGetDevState(GlobalVar.g_acsHandle, OneVoc.deviceID, null);

//                        if (GlobalVar.g_iTotalVoiceOpened == 0) {
//                            GlobalVar.My_BuildIndex(OneVoc.deviceID);
//                        }
						GlobalVar.g_iTotalVoiceFree.incrementAndGet();
						GlobalVar.g_iTotalVoiceOpened.incrementAndGet();
						GlobalVar.AllDeviceRes[bDspModuleID].lVocFreeNum.incrementAndGet();
						GlobalVar.AllDeviceRes[bDspModuleID].lVocOpened.incrementAndGet();

						break;
					}
					case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_CONFERENCE: {
						CONF_STRUCT OneConf = GlobalVar.GetConfStructByDevice(bDspModuleID, sChannelID);
						OneConf.uuid = "conf-" + bDspModuleID + "-" + sChannelID;
						OneConf.deviceID.m_CallID.m_s32FlowChannel = device.m_CallID.m_s32FlowChannel;
						OneConf.deviceID.m_CallID.m_s32FlowType = device.m_CallID.m_s32FlowType;

						GlobalVar.InitConfChannel(OneConf);

						DJAcsAPIDef.getInstance().XMS_ctsResetDevice(GlobalVar.g_acsHandle, OneConf.deviceID, null);
						DJAcsAPIDef.getInstance().XMS_ctsGetDevState(GlobalVar.g_acsHandle, OneConf.deviceID, null);
						DJAcsAPIDef.getInstance().XMS_ctsClearConf(GlobalVar.g_acsHandle, OneConf.deviceID, null);

						GlobalVar.g_iTotalConfFree.incrementAndGet();
						GlobalVar.g_iTotalConfOpened.incrementAndGet();
						GlobalVar.AllDeviceRes[bDspModuleID].lConfFreeNum.incrementAndGet();
						GlobalVar.AllDeviceRes[bDspModuleID].lConfOpened.incrementAndGet();

						break;
					}

					case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_DIGITAL_PORT: {
						PCM_STRUCT OnePcm = GlobalVar.GetPcmStructByDevice(bDspModuleID, sChannelID);

						OnePcm.deviceID.m_CallID.m_s32FlowChannel = device.m_CallID.m_s32FlowChannel;
						OnePcm.deviceID.m_CallID.m_s32FlowType = device.m_CallID.m_s32FlowType;

						DJAcsAPIDef.getInstance().XMS_ctsResetDevice(GlobalVar.g_acsHandle, OnePcm.deviceID, null);
						DJAcsAPIDef.getInstance().XMS_ctsGetDevState(GlobalVar.g_acsHandle, OnePcm.deviceID, null);

						GlobalVar.g_iTotalPcmOpened.incrementAndGet();
						GlobalVar.AllDeviceRes[bDspModuleID].lPcmOpened.incrementAndGet();
						break;
					}

				}
				break;
			}

			case XMS_EVT_TYPE.XMS_EVT_DEVICESTATE: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				DJEvtDeviceStateData acsDeviceState = (DJEvtDeviceStateData) obj;
				switch (device.m_s16DeviceMain) {
					case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_DIGITAL_PORT: {
						PCM_STRUCT OnePcm = GlobalVar.GetPcmStructByDevice(device.m_s8ModuleID,
							device.m_s16ChannelID);
						OnePcm.u8E1Type = (byte) ((acsDeviceState.acsGenerProcData.m_s32DeviceState >> 16) & 0xFF);
						OnePcm.s32AlarmVal = (acsDeviceState.acsGenerProcData.m_s32DeviceState) & 0xFFFF;
						break;
					}
					case XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH: {
						TRUNK_STRUCT OneTrk = GlobalVar.GetTrunkStructByDevice(device.m_s8ModuleID,
							device.m_s16ChannelID);
						OneTrk.iLineState = acsDeviceState.acsGenerProcData.m_s32DeviceState;
						log.info("line state change : {} 设备 m_s16ChannelID is {}, 设备 uuid is {}", 
							GetString.GetString_LineState(OneTrk.iLineState), device.m_s16ChannelID, OneTrk.uuid);
						if(OneTrk.iLineState == DEVICE_CALL_STATE.DCS_FREE && OneTrk.VocDevID != null && OneTrk.VocDevID.m_s16DeviceMain != 0) {
							log.warn("通道变为空闲，但是语音资源的关联还在，手动解除绑定...");
							GlobalVar.unlinkTrunkVoick(OneTrk);
						}
                        //如果设备变为空闲，外呼缓存中还存在设备ID，则手动发送一个外呼结束事件并清理通道资源
                        if(OneTrk.iLineState == DEVICE_CALL_STATE.DCS_FREE && GlobalVar.CALL_OUT_MAP.containsKey(OneTrk.uuid)) {
                            log.warn("通道变为空闲，但是还未收到此通道的呼叫结束事件，手动发送外呼事件...");
                            String[] callMsg = GlobalVar.CALL_OUT_MAP.remove(OneTrk.uuid);
                            EventData data = new EventData(OneTrk.uuid, OneTrk.lsh, null);
                            data.setState(-1);
                            data.setErrorCode(18);//用户拒接
                            data.setCaller(callMsg[0]);
                            data.setCallee(callMsg[1]);
                            DJKeygoeEvent event = new DJKeygoeEvent(EventSource.TRUNK, EventType.CALLOUT, data);
                            GlobalVar.sendEvent(event);
                            GlobalVar.ResetTrunk(OneTrk);
                            //发送消息提醒
                            String msg = String.format("未收到呼叫结束事件异常，平台ID：%s，主叫：%s，被叫：%s",CTIApplication.getBean(CommonSetting.class).getPlatformId(), data.getCaller(), data.getCallee());
                            CTIApplication.getBean(MqService.class).sendErrorMessage(msg, null);
                        }   
						break;
					}
				}
				break;
			}

			case XMS_EVT_TYPE.XMS_EVT_UNIFAILURE: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				break;
			}

			case XMS_EVT_TYPE.XMS_EVT_CLOSE_DEVICE: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				GlobalVar.CloseDeviceOK(device);
				break;
			}

			case XMS_EVT_TYPE.XMS_EVT_RESET_DEVICE: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				break;
			}
			//以上事件为系统事件，不需要通知server
			default: {
				GlobalVar.DispEventInfo(device, obj, nEvtType);
				GlobalVar.handlerEvent(device, acsEvtData, obj);
//                if (device.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH) {
//                    GlobalVar.TrunkWork(device, acsEvtData, obj);
//                } else if (device.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_VOICE) {
//                    DeviceID_t dev = GlobalVar.GetVoiceStructByDevice(device.m_s8ModuleID,
//                            device.m_s16ChannelID).UsedDevID;
//                    if (dev != null && dev.m_s16DeviceMain == XMS_DEVMAIN_TYPE.XMS_DEVMAIN_INTERFACE_CH) {
//                        GlobalVar.TrunkWork(dev, acsEvtData, obj);
//                    }
//                }
				break;
			}
		}

	}
}
