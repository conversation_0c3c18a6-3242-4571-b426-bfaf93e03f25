/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.djkeygoe.local;

import DJKeygoe.DeviceID_t;
import com.sungoin.cti.engine.impl.djkeygoe.Constants;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 *
 * <AUTHOR> 2015-5-4 会议设备
 */
public class CONF_STRUCT extends BASE_STRUCT {

	public DeviceID_t deviceID = new DeviceID_t();
	
	// ----------------
	public int State = -1;

	public AtomicInteger lMemberNum = new AtomicInteger();
	public AtomicInteger lListenNum = new AtomicInteger();

	public List<MEMBER_STRUCT> Member = new ArrayList<MEMBER_STRUCT>();
	public DeviceID_t recordDevID = null;
	public String recordCallUuid = null;
	public long beginTime;

	public void setHost(DeviceID_t device) {
		for (MEMBER_STRUCT m : Member) {
			if (isDeviceEqual(m.DevID, device)) {
				m.isHost = true;
				break;
			}
		}
	}

	public DeviceID_t getHostDevice() {
		for (MEMBER_STRUCT m : Member) {
			if (m.isHost) {
				return m.DevID;
			}
		}
		return null;
	}

	public synchronized MEMBER_STRUCT getMember(DeviceID_t deviceId) {
		for (MEMBER_STRUCT m : Member) {
			if (isDeviceEqual(m.DevID, deviceId)) {
				return m;
			}
		}
		return null;
	}

	public synchronized void addMember(MEMBER_STRUCT member) {
		this.Member.add(member);
		this.lMemberNum.incrementAndGet();
		if (member.lMode == Constants.CONF_MODE_LISTEN) {
			this.lListenNum.incrementAndGet();
		}
	}

	public synchronized void leaveMember(MEMBER_STRUCT member) {
		member.isLeaved = true;
		this.lMemberNum.decrementAndGet();
		if (member.lMode == Constants.CONF_MODE_LISTEN) {
			this.lListenNum.decrementAndGet();
		}
	}

	public synchronized MEMBER_STRUCT removeMember(DeviceID_t deviceId) {
		int index = 0;
		MEMBER_STRUCT member = null;
		for (int i = 0; i < Member.size(); i++) {
			MEMBER_STRUCT m = Member.get(i);
			if(m.isLeaved && isDeviceEqual(m.DevID, deviceId)) {
				member = m;
				index = i;
				break;
			}
		}
		if(member != null) {
			Member.remove(index);
		}
		return member;
	}

	public void clear() {
		this.lListenNum.set(0);
		this.lMemberNum.set(0);
		this.recordDevID = null;
		this.recordCallUuid = null;
		Member = new ArrayList<MEMBER_STRUCT>();
	}

	public boolean isDeviceEqual(DeviceID_t dev1, DeviceID_t dev2) {
		return (dev1.m_s16DeviceMain == dev2.m_s16DeviceMain && dev1.m_s8ModuleID == dev2.m_s8ModuleID
			&& dev1.m_s16ChannelID == dev2.m_s16ChannelID);
	}

}

class MEMBER_STRUCT {
	public long joinTime = System.currentTimeMillis();
	public int lMode;
	public DeviceID_t DevID;
	public String callDeviceId;
	public int callLsh;
	public boolean isHost = false;
	public boolean isLeaved = false;

	public MEMBER_STRUCT(int lMode, DeviceID_t DevID, String callDeviceId, int callLsh) {
		this.lMode = lMode;
		this.DevID = DevID;
		this.callDeviceId = callDeviceId;
		this.callLsh = callLsh;
	}

	public String getModeString() {
		switch (lMode) {
			case 1:
				return "ADD";
			case 2:
				return "LISTEN";
			case 3:
				return "SPEAKONLY";
			case 4:
				return "PLAY";
			default:
				return "";
		}
	}
}
