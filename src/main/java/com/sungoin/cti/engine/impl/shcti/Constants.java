/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti;

/**
 *
 * <AUTHOR> 2015-11-26
 */
public class Constants {
	public static final int STATE_SUCCESS = 1;

	public static final String PROP_PATH = "shcti/shcti.properties";
	public static final String SHCTI_SERVER_IP = "shcti.server.ip";
	public static final String SHCTI_SERVER_PORT = "shcti.server.port";
	
	/* 系统类消息及响应  */
	public static final String SYS_LINK = "0001";
	public static final String SYS_LINK_REPLY = "0002";
	public static final String SYS_HEARTBEAT = "0003";
	public static final String SYS_HEARTBEAT_REPLY = "0004";

	/* 事件类消息及响应  */
	public static final String EVENT_CALLINCOME = "1001";
	public static final String EVENT_CALLINCOME_REPLY = "1002";
	public static final String EVENT_ALERT = "1003";
	public static final String EVENT_ALERT_REPLY = "1004";
	public static final String EVENT_ANSWER = "1005";
	public static final String EVENT_ANSWER_REPLY = "1006";
	public static final String EVENT_CALLOUT = "1007";
	public static final String EVENT_CALLOUT_REPLY = "1008";
	public static final String EVENT_PLAYEND = "1009";
	public static final String EVENT_PLAYEND_REPLY = "1010";
	public static final String EVENT_RECORDEND = "1011";
	public static final String EVENT_RECORDEND_REPLY = "1012";
	public static final String EVENT_DTMF = "1013";
	public static final String EVENT_DTMF_REPLY = "1014";
	public static final String EVENT_ONHOOK = "1015";
	public static final String EVENT_ONHOOK_REPLY = "1016";
	
	/* 指令类消息及响应  */
	public static final String CMD_ALERT = "0101";
	public static final String CMD_ALERT_REPLY = "0102";
	public static final String CMD_ANSWER = "0103";
	public static final String CMD_ANSWER_REPLY = "0104";
	public static final String CMD_PLAY = "0105";
	public static final String CMD_PLAY_REPLY = "0106";
	public static final String CMD_STOPPLAY = "0107";
	public static final String CMD_STOPPLAY_REPLY = "0108";
	public static final String CMD_RECORD = "0109";
	public static final String CMD_RECORD_REPLY = "0110";
	public static final String CMD_STOPRECORD = "0111";
	public static final String CMD_STOPRECORD_REPLY = "0112";
	public static final String CMD_ONHOOK = "0113";
	public static final String CMD_ONHOOK_REPLY = "0114";
	public static final String CMD_CALL = "0115";
	public static final String CMD_CALL_REPLY = "0116";
	public static final String CMD_CONNECT = "0117";
	public static final String CMD_CONNECT_REPLY = "0118";
	public static final String CMD_DISCONNECT = "0119";
	public static final String CMD_DISCONNECT_REPLY = "0120";
	public static final String CMD_STATISTIC = "0121";
	public static final String CMD_STATISTIC_REPLY = "0122";
	public static final String CMD_LINESTATE = "0123";
	public static final String CMD_LINESTATE_REPLY = "0124";
}
