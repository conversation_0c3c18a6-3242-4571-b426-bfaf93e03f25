/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

package com.sungoin.cti.engine.impl.shsip;

import com.sungoin.cti.engine.exception.InitEngineFailException;
import java.io.IOException;
import java.util.Properties;

/**
 * 
 * <AUTHOR>
 * 2015-5-4
 */
public class Configuration {
	private static final Properties prop = new Properties();

	private Configuration() {
	}
	
	static {
		try {
			prop.load(Configuration.class.getClassLoader().getResourceAsStream(Constants.PROP_PATH));
		} catch (IOException ex) {
			throw new InitEngineFailException("can not load shsip.properties!", ex);
		}
	}
	
	public static String getProperty(String key) {
		return prop.getProperty(key);
	}
	
	public static String getProperty(String key, String defaultValue) {
		return prop.getProperty(key, defaultValue);
	}
    
    public static String getHeartBeatSeconds() {
        return prop.getProperty(Constants.HEART_BEAT_SECONDS);
    }
    
    public static String getMasterId() {
        return prop.getProperty(Constants.MASTER_ID);
    }
    
    public static String getLocalCode() {
        return prop.getProperty(Constants.LOCAL_CODE);
    }
    
    public static String getMachineNo() {
        return prop.getProperty(Constants.MACHINE_NO);
    }
    
    public static String getCalleePrefix() {
        return prop.getProperty(Constants.CALLEE_PREFIX);
    }
    
    public static String getTestCalller() {
        return prop.getProperty(Constants.TEST_CALLER);
    }
}
