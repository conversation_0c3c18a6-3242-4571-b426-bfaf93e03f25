/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip.socket;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.impl.shsip.Configuration;
import com.sungoin.cti.engine.impl.shsip.Constants;
import com.sungoin.cti.engine.impl.shsip.ShsipEngine;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.transport.socket.nio.NioSocketConnector;

/**
 *
 * <AUTHOR>
 */
public class ShsipSocketClient {
    
    private static final Log log = LogFactory.getLog(ShsipSocketClient.class);
    
    private final CTIEngine engine;
    
    private final MessageProcesser mp;

	/**
	 * The cf.
	 */
	private ConnectFuture cf = null;

	/**
	 * The connector.
	 */
	private NioSocketConnector connector = null;

	/**
	 * The is connected.
	 */
	public volatile boolean isConnected = false;

	/**
	 * The terminated.
	 */
	public volatile boolean terminated = false;
    
	/**
	 * Gets the cf.
	 *
	 * @return the cf
	 */
	public ConnectFuture getCf() {
		return cf;
	}

    public ShsipSocketClient(CTIEngine engine) {
        this.engine = engine;
        this.mp = new MessageProcesser(engine);
    }
    
    /**
	 * Inits the connector.
	 *
	 * @return true, if successful
	 */
	public synchronized boolean initConnector() {
		try {
			connector = new NioSocketConnector();
			InetAddress address = InetAddress.getByName(Configuration.getProperty(Constants.SHSIP_SERVER_IP));
//			connector.getFilterChain().addLast("logger", new LoggingFilter());
			connector.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
			connector.setHandler(new ShsipClientHandler(this));// 设置事件处理器            
			cf = connector.connect(new InetSocketAddress(address, Integer.valueOf(Configuration.getProperty(Constants.SHSIP_SERVER_PORT))));// 建立连接
			//等待连接创建完成,
			//如果不加这句则在连接异常时getSession()并不会抛异常,获得的SESSION为null
			cf.awaitUninterruptibly(1000);
			if (cf.isConnected()) {
				isConnected = true;
				log.info("shsip socket server 连接成功");
			} else {
				isConnected = false;
				connector.dispose();
				log.warn("shsip socket server 连接失败");
			}
		} catch (Exception e) {
			isConnected = false;
			connector.dispose();
			log.error("在ShsipSocketManager中的connect方法中出错原因是:", e);
		}
		return isConnected;
	}

	/**
	 * Interrupt.
	 */
	public synchronized void interrupt() {
		isConnected = false;
		if (connector != null && !connector.isDisposed()) {
			connector.dispose();
		}
	}

	/**
	 * Close.
	 */
	public synchronized void close() {
		isConnected = false;
		terminated = true;
		connector.dispose();
	}

    public CTIEngine getEngine() {
        return engine;
    }
    
    public MessageProcesser getMessageProcesser() {
		return mp;
	}

	public void sendMessage(SipMessage message) {
		try {
            log.debug("发送消息到SIP服务器：" + message);
			cf.getSession().write(message.getIoBuffer());// 发送消息
		} catch (Exception e) {
			log.error("在MinaClient中的sendMessage方法中出错原因是:" + e.getMessage());
		}
	}
    
    public static void main(String[] args) throws InterruptedException {
        final ShsipSocketClient client = new ShsipSocketClient(null);
//        client.getMessageProcesser().start();
        
        client.initConnector();
        Thread.sleep(1000);
        client.sendMessage(MessageBuilder.getLogoutMessage());
        
        //发送注册消息
        SipMessage registerMessage = MessageBuilder.getRegisterMessage();
        client.sendMessage(registerMessage);
        new Thread(new Runnable(){
            @Override
            public void run() {
                while(true) {
                    try {
                        Thread.sleep(10000);
                    } catch (Exception e) {
                    }
                    //发送心跳消息
                    client.sendMessage(MessageBuilder.getHeartBeatMessage());
                }
            }
            
        }).start();
        Thread.sleep(5000);
        //发送注销消息
//        client.sendMessage(SipMessage.getLogoutMessage());
        //发送外呼消息
//        client.sendMessage(MessageBuilder.getCallOutMessage("10106868", "13701836419", ShsipEngine.getNextCalloutLsh(), 40));
//        Thread.sleep(20000);
//        client.sendMessage(MessageBuilder.getOnhookMessage("000000000", 1));
        
    }
}
