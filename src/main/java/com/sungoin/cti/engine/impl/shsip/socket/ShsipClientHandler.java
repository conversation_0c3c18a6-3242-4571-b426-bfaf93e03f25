/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip.socket;

import com.sungoin.cti.engine.impl.shsip.ShsipEngine;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class ShsipClientHandler extends IoHandlerAdapter {

    private static final Logger LOG = LoggerFactory.getLogger(ShsipClientHandler.class);

    private final ShsipSocketClient client;

    public ShsipClientHandler(ShsipSocketClient client) {
        this.client = client;
    }
    
    @Override
	public void messageReceived(IoSession session, Object message) throws Exception {
		String msg = message.toString();
		LOG.debug("message received : {}", msg);
        client.getMessageProcesser().process(msg);
	}

	@Override
	public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
		LOG.error("exceptionCaught method was called..." + cause.getMessage(), cause);
		client.interrupt();
	}

	/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionClosed(org.apache.mina.core.session.IoSession)
	 */
	@Override
	public void sessionClosed(IoSession session) throws Exception {
		LOG.warn("sessionClosed method was called!");
		client.interrupt();
		while (!client.isConnected) {
			if (client.terminated) {
				break;
			}
			try {
				Thread.sleep(10000);
				LOG.info("开始重新连接SIP服务器：");
				boolean connected = client.initConnector();
				if(connected) {
                    LOG.info("开始重新注册SIP服务器：");
					((ShsipEngine)client.getEngine()).logout();
                    ((ShsipEngine)client.getEngine()).login();
				}
			} catch (Exception ex) {
				LOG.error("重新连接板卡服务器失败！" + ex.getMessage());
			}
		}
	}
}
