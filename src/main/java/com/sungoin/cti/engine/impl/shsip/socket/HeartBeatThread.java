/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip.socket;

import com.sungoin.cti.engine.EngineState;
import com.sungoin.cti.engine.impl.shsip.Configuration;
import com.sungoin.cti.engine.impl.shsip.ShsipEngine;

/**
 *
 * <AUTHOR>
 */
public class HeartBeatThread implements Runnable {
    private final int heartbeatSeconds;
    private final ShsipEngine engine;

    public HeartBeatThread(ShsipEngine engine) {
        this.heartbeatSeconds = Integer.valueOf(Configuration.getHeartBeatSeconds());
        this.engine = engine;
    }
    
    public void start() {
        new Thread(this).start();
    }
    
    @Override
    public void run() {
        while(engine.getState() == EngineState.RUNNING) {
            try {
                Thread.sleep(1000 * heartbeatSeconds);
            } catch (Exception e) {
            }
            engine.heartBeat();
        }
        System.out.println("HeartBeatThread exit...");
    }
}
