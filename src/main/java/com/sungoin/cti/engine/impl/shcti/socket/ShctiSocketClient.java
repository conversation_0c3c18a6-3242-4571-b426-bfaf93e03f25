/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti.socket;

import com.sungoin.cti.engine.impl.shcti.Configuration;
import com.sungoin.cti.engine.impl.shcti.Constants;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.nio.charset.Charset;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.mina.core.buffer.IoBuffer;
import org.apache.mina.core.future.ConnectFuture;
import org.apache.mina.filter.codec.ProtocolCodecFilter;
import org.apache.mina.filter.codec.textline.TextLineCodecFactory;
import org.apache.mina.transport.socket.nio.NioDatagramConnector;

/**
 *
 * <AUTHOR> 2015-11-27
 */
public class ShctiSocketClient {

	private static final Log log = LogFactory.getLog(ShctiSocketClient.class);

	/**
	 * The cf.
	 */
	private ConnectFuture cf = null;

	/**
	 * The connector.
	 */
	private NioDatagramConnector connector = null;

	/**
	 * The is connected.
	 */
	public volatile boolean isConnected = false;

	/**
	 * The terminated.
	 */
	public volatile boolean terminated = false;

	/**
	 * Gets the cf.
	 *
	 * @return the cf
	 */
	public ConnectFuture getCf() {
		return cf;
	}

	/**
	 * Inits the connector.
	 *
	 * @return true, if successful
	 */
	public synchronized boolean initConnector() {
		try {
			connector = new NioDatagramConnector();
			InetAddress address = InetAddress.getByName(Configuration.getProperty(Constants.SHCTI_SERVER_IP));
//			connector.getFilterChain().addLast("logger", new LoggingFilter());
			connector.getFilterChain().addLast("codec", new ProtocolCodecFilter(new TextLineCodecFactory(Charset.forName("UTF-8"))));
			connector.setHandler(new ShctiClientHandler(this));// 设置事件处理器
			cf = connector.connect(new InetSocketAddress(address, Integer.valueOf(Configuration.getProperty(Constants.SHCTI_SERVER_PORT))));// 建立连接
			//等待连接创建完成,
			//如果不加这句则在连接异常时getSession()并不会抛异常,获得的SESSION为null
			cf.awaitUninterruptibly(1000);
			if (cf.isConnected()) {
				isConnected = true;
				log.info("shcti socket server 连接成功");
			} else {
				isConnected = false;
				connector.dispose();
				log.warn("shcti socket server 连接失败");
			}
		} catch (Exception e) {
			isConnected = false;
			connector.dispose();
			log.error("在ShctiSocketManager中的connect方法中出错原因是:", e);
		}
		return isConnected;
	}

	/**
	 * Interrupt.
	 */
	public synchronized void interrupt() {
		isConnected = false;
		if (connector != null && !connector.isDisposed()) {
			connector.dispose();
		}
	}

	/**
	 * Close.
	 */
	public synchronized void close() {
		isConnected = false;
		terminated = true;
		connector.dispose();
	}

	public void sendMessage(String message) {
		try {
			log.debug("发送到板卡服务器消息：" + message);
			IoBuffer buffer = IoBuffer.wrap(message.getBytes());
			cf.getSession().write(buffer);// 发送消息
		} catch (Exception e) {
			log.error("在MinaClient中的sendMessage方法中出错原因是:" + e.getMessage());
		}
	}
}
