/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shsip;

import com.sungoin.cti.engine.CTIEvent;
import com.sungoin.cti.engine.EventData;
import com.sungoin.cti.engine.EventSource;
import com.sungoin.cti.engine.EventType;

/**
 *
 * <AUTHOR>
 */
public class ShSipEvent implements CTIEvent {
    private final EventSource source;
	private final EventType type;
	private final EventData data;

	public ShSipEvent(EventSource source, EventType type, EventData data) {
		this.source = source;
		this.type = type;
		this.data = data;
	}
	
	@Override
	public EventSource getEventSource() {
		return source;
	}

	@Override
	public EventType getEventType() {
		return type;
	}

	@Override
	public EventData getEventData() {
		return data;
	}
}
