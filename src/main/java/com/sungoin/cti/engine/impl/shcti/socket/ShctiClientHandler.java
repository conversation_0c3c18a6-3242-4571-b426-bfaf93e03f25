/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.shcti.socket;

import com.sungoin.cti.engine.impl.shcti.Configuration;
import org.apache.mina.core.service.IoHandlerAdapter;
import org.apache.mina.core.session.IdleStatus;
import org.apache.mina.core.session.IoSession;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR> 2015-11-27
 */
public class ShctiClientHandler extends IoHandlerAdapter {

	private static final Logger LOG = LoggerFactory.getLogger(ShctiClientHandler.class);
	
	private final ShctiSocketClient client;

	public ShctiClientHandler(ShctiSocketClient client) {
		this.client = client;
	}
	
	@Override
	public void messageReceived(IoSession session, Object message) throws Exception {
		String msg = message.toString();
		LOG.debug("message received : {}", msg);
	}

	@Override
	public void exceptionCaught(IoSession session, Throwable cause) throws Exception {
		LOG.error("exceptionCaught method was called..." + cause.getMessage(), cause);
		client.interrupt();
	}

	/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionClosed(org.apache.mina.core.session.IoSession)
	 */
	@Override
	public void sessionClosed(IoSession session) throws Exception {
		LOG.warn("sessionClosed method was called!");
		client.interrupt();
		while (!client.isConnected) {
			if (client.terminated) {
				break;
			}
			try {
				Thread.sleep(10000);
				LOG.info("开始重新连接板卡服务器：");
				boolean connected = client.initConnector();
				if(connected) {
					client.sendMessage(MessageBuild.getLinkMessage());
				}
			} catch (Exception ex) {
				LOG.error("重新连接板卡服务器失败！" + ex.getMessage());
			}
		}
	}

	/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionCreated(org.apache.mina.core.session.IoSession)
	 */
	@Override
	public void sessionCreated(IoSession session) throws Exception {
		session.getConfig().setIdleTime(IdleStatus.BOTH_IDLE, Integer.valueOf(Configuration.getProperty("shcti.heartbeat.seconds")));
	}

	/* (non-Javadoc)
     * @see org.apache.mina.core.service.IoHandlerAdapter#sessionIdle(org.apache.mina.core.session.IoSession, org.apache.mina.core.session.IdleStatus)
	 */
	@Override
	public void sessionIdle(IoSession session, IdleStatus status) throws Exception {
		if (status == IdleStatus.BOTH_IDLE) {
			session.write(MessageBuild.getHeartBeatMessage());
		}
	}
}
