/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.engine.impl.djkeygoe.local;

import com.sungoin.cti.engine.impl.djkeygoe.Constants;
import com.sungoin.cti.engine.exception.InitEngineFailException;
import java.io.IOException;
import java.util.Properties;

/**
 *
 * <AUTHOR> 2015-5-4
 */
public class Configuration {

	private static final Properties prop = new Properties();

	public static int ISUP_spOriginalCalledNumber_m_u8NatureOfAddressIndicator = 0;
	public static int ISUP_spOriginalCalledNumber_m_u8OddEvenIndicator = 0;
	public static int ISUP_spOriginalCalledNumber_m_u8AddressPresentationRestrictedIndicator = 0;
	public static int ISUP_spOriginalCalledNumber_m_u8NumberingPlanIndicator = 0;

	public static int ISUP_spRedirectingNumber_m_u8NatureOfAddressIndicator = 0;
	public static int ISUP_spRedirectingNumber_m_u8OddEvenIndicator = 0;
	public static int ISUP_spRedirectingNumber_m_u8AddressPresentationRestrictedIndicator = 0;
	public static int ISUP_spRedirectingNumber_m_u8NumberingPlanIndicator = 0;

	public static int ISUP_spRedirectionInformation_m_u8RedirectingIndicator = 0;
	public static int ISUP_spRedirectionInformation_m_u8OriginalRedirectionReason = 0;
	public static int ISUP_spRedirectionInformation_m_u8RedirectionCounter = 0;
	public static int ISUP_spRedirectionInformation_m_u8RedirectingReason = 0;
    
    public static int ISUP_SP_BackwardCallIndicator_m_u8EndToEndInformationIndicator = 0;
	
	public static int LOCAL_MOBILE = 1;
	public static int FREE_TRUNK_RULE = 0;
	
	public static int SEND_Q_SIZE = 32;
	public static int RECV_Q_SIZE = 32;

    public static boolean CALLER_KEEP_PREFIX = false;
    public static boolean CALL_INDICATOR = false;
	private Configuration() {
	}

	static {
		try {
			prop.load(Configuration.class.getClassLoader().getResourceAsStream(Constants.PROP_PATH));
			ISUP_spOriginalCalledNumber_m_u8NatureOfAddressIndicator = Integer.valueOf(prop.getProperty("ISUP_spOriginalCalledNumber.m_u8NatureOfAddressIndicator"));
			ISUP_spOriginalCalledNumber_m_u8OddEvenIndicator = Integer.valueOf(prop.getProperty("ISUP_spOriginalCalledNumber.m_u8OddEvenIndicator"));
			ISUP_spOriginalCalledNumber_m_u8AddressPresentationRestrictedIndicator = Integer.valueOf(prop.getProperty("ISUP_spOriginalCalledNumber.m_u8AddressPresentationRestrictedIndicator"));
			ISUP_spOriginalCalledNumber_m_u8NumberingPlanIndicator = Integer.valueOf(prop.getProperty("ISUP_spOriginalCalledNumber.m_u8NumberingPlanIndicator"));
			
			ISUP_spRedirectingNumber_m_u8NatureOfAddressIndicator = Integer.valueOf(prop.getProperty("ISUP_spRedirectingNumber.m_u8NatureOfAddressIndicator"));
			ISUP_spRedirectingNumber_m_u8OddEvenIndicator = Integer.valueOf(prop.getProperty("ISUP_spRedirectingNumber.m_u8OddEvenIndicator"));
			ISUP_spRedirectingNumber_m_u8AddressPresentationRestrictedIndicator = Integer.valueOf(prop.getProperty("ISUP_spRedirectingNumber.m_u8AddressPresentationRestrictedIndicator"));
			ISUP_spRedirectingNumber_m_u8NumberingPlanIndicator = Integer.valueOf(prop.getProperty("ISUP_spRedirectingNumber.m_u8NumberingPlanIndicator"));
			
			ISUP_spRedirectionInformation_m_u8RedirectingIndicator = Integer.valueOf(prop.getProperty("ISUP_spRedirectionInformation.m_u8RedirectingIndicator"));
			ISUP_spRedirectionInformation_m_u8OriginalRedirectionReason = Integer.valueOf(prop.getProperty("ISUP_spRedirectionInformation.m_u8OriginalRedirectionReason"));
			ISUP_spRedirectionInformation_m_u8RedirectionCounter = Integer.valueOf(prop.getProperty("ISUP_spRedirectionInformation.m_u8RedirectionCounter"));
			ISUP_spRedirectionInformation_m_u8RedirectingReason = Integer.valueOf(prop.getProperty("ISUP_spRedirectionInformation.m_u8RedirectingReason"));
            ISUP_SP_BackwardCallIndicator_m_u8EndToEndInformationIndicator = Integer.valueOf(prop.getProperty("ISUP_SP_BackwardCallIndicator.m_u8EndToEndInformationIndicator", "0"));
			
			LOCAL_MOBILE = Integer.valueOf(prop.getProperty("local_mobile"));
			
			FREE_TRUNK_RULE = Integer.valueOf(prop.getProperty("free_trunk_rule"));
            
            if(prop.containsKey("caller_keep_prefix")) {
                CALLER_KEEP_PREFIX = Boolean.valueOf(prop.getProperty("caller_keep_prefix"));
            }
			if(prop.containsKey("send_q_size")) {
                SEND_Q_SIZE = Integer.valueOf(prop.getProperty("send_q_size"));
            }
			if(prop.containsKey("recv_q_size")) {
                RECV_Q_SIZE = Integer.valueOf(prop.getProperty("recv_q_size"));
            }
            if(prop.containsKey("callindicator")) {
                CALL_INDICATOR = Boolean.valueOf(prop.getProperty("callindicator"));
            }
		} catch (IOException ex) {
			throw new InitEngineFailException("can not load xms.properties!", ex);
		}
	}

	public static String getProperty(String key) {
		return prop.getProperty(key);
	}

	public static String getProperty(String key, String defaultValue) {
		return prop.getProperty(key, defaultValue);
	}
}
