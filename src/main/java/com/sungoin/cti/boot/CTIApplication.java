/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package com.sungoin.cti.boot;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ImportResource;

import com.sungoin.cti.engine.CTIEngine;
import com.sungoin.cti.engine.impl.djkeygoe.DJKeygoeEngine;
import com.sungoin.cti.engine.impl.xcc.XccEngine;
import com.sungoin.cti.engine.impl.shcti.ShctiEngine;
import com.sungoin.cti.engine.impl.shsip.ShsipEngine;
import com.sungoin.cti.server.config.CommonSetting;
import com.sungoin.cti.server.scoket.CTIServer;
import com.sungoin.cti.server.service.ProcessService;
import com.sungoin.cti.util.SpringHelper;
import org.springframework.context.ApplicationContext;

/**
 * <AUTHOR> 2015-5-4
 */
@SpringBootApplication
@ComponentScan(basePackages = { "com.sungoin.cti" })
@EnableConfigurationProperties({ CommonSetting.class })
@ImportResource(value = { "classpath:/ApplicationContext-mail.xml" })
public class CTIApplication {

    private static ApplicationContext context;
    private static final Logger log = LoggerFactory.getLogger(CTIApplication.class);

    @Resource
    private CommonSetting commonSetting;

    @Bean
    public CTIEngine CTIEngine() {
        String engineImpl = this.commonSetting.getEngineImpl();
        switch (engineImpl) {
            case "keygoe":
                return new DJKeygoeEngine();
            case "shcti":
                return new ShctiEngine();
            case "shsip":
                return new ShsipEngine();
            case "xcc":
                return new XccEngine();
            default:
                return null;
        }
    }

    public static void main(String[] args) throws InterruptedException {
        log.info("cti server start...");
        context = SpringApplication.run(CTIApplication.class, args);

        ProcessService processService = SpringHelper
                .getSpringBean(ProcessService.class);

        String tableName = processService.getTableName();
        if (!processService.tableExist(tableName)) {
            processService.createTable(tableName);
        }

        String nextDayTableName = processService.getNextDayTableName();
        if (!processService.tableExist(nextDayTableName)) {
            processService.createTable(nextDayTableName);
        }
//		processService.resetAgentStatus();
        CTIServer server = (CTIServer) SpringHelper
                .getSpringBean(SpringHelper.CTI_SERVER);
        server.start();
        processService.resetConnectTrunk();
        Runtime.getRuntime().addShutdownHook(new Thread(
                () -> {
                    ProcessService.log.info("执行CTI停止前的清理工作。。。");
                    processService.cleanConnectTrunk();
                }
        ));
    }
    
    public static <T> T  getBean(Class<T> cls) {
		return context.getBean(cls);
	}
}
