#平台ID
platform_id=17

#接收邮件通知人员邮箱，以逗号分隔；
personal_mail=<EMAIL>

#中继使用情况大于此值，邮件通知
usage_rate=-1

#通道使用率检查频率（cron表达式）
jobs.schedule=0/30 * * * * ?

#CTI引擎配置(keygoe或shcti或shsip或xcc)
engine_impl = xcc

#指定呼转外呼时的原始小号
allway_caller =

#原被叫及改发号码前缀（原被叫前缀:改发号码前缀）空表示不做修改eg:（021:21 or 021: or :021）
#如果配置-1表示截取1位，-2表示截取2位，以此类推
prefix =
 
#平台名称
platform_name = 上海电信

#被叫号码前缀
callee_prefix = 

#接通后多少时间后强制拆线(单位：秒),可选配置，不配置则无限制
call_max_seconds = 60

#超长通话检查间隔（单位：毫秒）,必须配置，不配置会出错
check_call_delay = 60000

#RSC检查间隔（单位：毫秒）,必须配置，不配置会出错
rsc_check_delay = 60000