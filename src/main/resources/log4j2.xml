<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
	<Appenders>
		<Console name="STDOUT" target="SYSTEM_OUT">
			<PatternLayout pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} - %msg%n"/>
		</Console>
		<RollingRandomAccessFile name="ENGINGFILE" fileName="logs/CTIENGINE/ctiengine.log"
								 filePattern="logs/CTIENGINE/%d{yyyy-MM-dd}/ctiengine-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} %p %c{3.} [%t] %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
		<RollingRandomAccessFile name="SERVERFILE" fileName="logs/CTISERVER/ctiserver.log"
								 filePattern="logs/CTISERVER/%d{yyyy-MM-dd}/ctiserver-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} %p %c{3.} [%t] %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
		<RollingRandomAccessFile name="KEYGOEFILE" fileName="logs/KEYGOE/keygoe.log"
								 filePattern="logs/KEYGOE/%d{yyyy-MM-dd}/keygoe-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} %p %c{3.} [%t] %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
		<RollingRandomAccessFile name="SHCTIFILE" fileName="logs/SHCTI/shcti.log"
								 filePattern="logs/SHCTI/%d{yyyy-MM-dd}/shcti-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} %p %c{3.} [%t] %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
        
        <RollingRandomAccessFile name="SHSIPFILE" fileName="logs/SHSIP/shsip.log"
								 filePattern="logs/SHSIP/%d{yyyy-MM-dd}/shsip-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} %p %c{3.} [%t] %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
        
        <RollingRandomAccessFile name="XCC" fileName="logs/XCC/xcc.log"
								 filePattern="logs/XCC/%d{yyyy-MM-dd}/xcc-%i.log.gz">
			<PatternLayout>
				<Pattern>%d{HH:mm:ss.SSS} %p %c{3.} [%t] %m%n</Pattern>
			</PatternLayout>
			<Policies>
				<TimeBasedTriggeringPolicy />
				<SizeBasedTriggeringPolicy size="10 MB"/>
			</Policies>
			<DefaultRolloverStrategy max="20"/>
		</RollingRandomAccessFile>
	</Appenders>
	<Loggers>
		<logger name="com.sungoin.cti.engine.impl.djkeygoe.local" level="DEBUG" additivity="false">
			<appender-ref ref="KEYGOEFILE" />
		</logger>  
		
		<logger name="com.sungoin.cti.engine.impl.shcti" level="DEBUG" additivity="false">
			<appender-ref ref="SHCTIFILE" />
		</logger>  
        
        <logger name="com.sungoin.cti.engine.impl.shsip" level="DEBUG" additivity="false">
			<appender-ref ref="SHSIPFILE" />
		</logger>  
        
        <logger name="com.sungoin.cti.engine.impl.xcc" level="DEBUG" additivity="false">
			<appender-ref ref="XCC" />
		</logger>  
        
		<logger name="com.sungoin.cti.engine" level="DEBUG" additivity="false">
			<appender-ref ref="ENGINGFILE" />
		</logger>
	
		<logger name="com.sungoin.cti.server" level="DEBUG" additivity="false">
			<appender-ref ref="SERVERFILE" />
		</logger>  
        
		<Root level="WARN">
			<appender-ref ref="STDOUT"/>
		</Root>
	</Loggers>
</Configuration>