# To change this license header, choose License Headers in Project Properties.
# To change this template file, choose Tools | Templates
# and open the template in the editor.
IpAddr=***************
Port=9000
UserName=
PassWord=
VocPath=C:/DJKeygoe/Samples/Voc
VoiceRule=1
DebugOn=1

MAX_DSP_MODULE_NUMBER_OF_XMS=4
MAX_VOC_RES_NUM_EACH_DSP=128
MAX_PCM_RES_NUM_EACH_DSP=4
MAX_TRUNK_RES_NUM_EACH_DSP=128
MAX_CONF_RES_NUM_EACH_DSP=64

####################信令参数设置
####################原始被叫参数
#地址性质
ISUP_spOriginalCalledNumber.m_u8NatureOfAddressIndicator = 1
#奇偶指示码
ISUP_spOriginalCalledNumber.m_u8OddEvenIndicator = 0
#显示指示
ISUP_spOriginalCalledNumber.m_u8AddressPresentationRestrictedIndicator = 0
#号码计划指示码
ISUP_spOriginalCalledNumber.m_u8NumberingPlanIndicator = 1
#####################改发号码参数
#地址性质指示码
ISUP_spRedirectingNumber.m_u8NatureOfAddressIndicator = 3
#奇偶指示码
ISUP_spRedirectingNumber.m_u8OddEvenIndicator = 0
#显示限制指示码
ISUP_spRedirectingNumber.m_u8AddressPresentationRestrictedIndicator = 0
#号码计划指示码
ISUP_spRedirectingNumber.m_u8NumberingPlanIndicator = 1
######################改发信息参数
#改发指示码
ISUP_spRedirectionInformation.m_u8RedirectingIndicator = 3
#原来的改发原因
ISUP_spRedirectionInformation.m_u8OriginalRedirectionReason = 3
#改发计数器
ISUP_spRedirectionInformation.m_u8RedirectionCounter = 1
#改发原因
ISUP_spRedirectionInformation.m_u8RedirectingReason = 3

#后向呼叫指示码
ISUP_SP_BackwardCallIndicator.m_u8EndToEndInformationIndicator = 0

#本地手机主叫性质码(3或者1或者0，0走默认规则)
local_mobile = 3

#空闲通道查找规则（0：每次从头开始寻找，1：每次从上次找到位置往下寻找）
free_trunk_rule=1

#外地手机送的主叫参数是否保留0
caller_keep_prefix = false

#发送队列大小（kb）
send_q_size = 32
#接收队列大小（kb）
recv_q_size = 32

#任选后向呼叫指示码参数
callindicator = true