<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:jee="http://www.springframework.org/schema/jee"
    xmlns:util="http://www.springframework.org/schema/util" xmlns:aop="http://www.springframework.org/schema/aop"
    xmlns:context="http://www.springframework.org/schema/context"
    xmlns:tx="http://www.springframework.org/schema/tx" xmlns:mvc="http://www.springframework.org/schema/mvc"
    xmlns:jpa="http://www.springframework.org/schema/data/jpa"
    xmlns:task="http://www.springframework.org/schema/task"
    xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
                        http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd
                        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
                        http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
                        http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.0.xsd
                        http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.2.xsd
                        http://www.springframework.org/schema/util http://www.springframework.org/schema/util/spring-util-3.2.xsd
                        http://www.springframework.org/schema/data/jpa http://www.springframework.org/schema/data/jpa/spring-jpa.xsd 
                        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd
    ">
       
    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource" />
    </bean>

    <bean id="dataSource" class="org.apache.commons.dbcp2.BasicDataSource"
        destroy-method="close" lazy-init="false">
        <property name="driverClassName" value="com.mysql.jdbc.Driver" />
		
        <property name="url">
        <value>*******************************************************************************************************************************</value>
        </property>
        <property name="username" value="cti_server" />
        <property name="password" value="cti_server@220429#" />
		
        <property name="initialSize" value="1" />
        <property name="maxTotal" value="50" />
        <property name="maxIdle" value="10" />
        <property name="minIdle" value="1" />
        <property name="maxWaitMillis" value="60000" />
        <property name="removeAbandonedOnBorrow" value="true" />
        <property name="removeAbandonedTimeout" value="180" />
        <!-- 重连配置 -->
        <property name="testWhileIdle" value="true" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="validationQuery" value="select 1 from dual" />
        <property name="validationQueryTimeout" value="1" />
        <property name="timeBetweenEvictionRunsMillis" value="600000" />
        <property name="numTestsPerEvictionRun" value="50" />
    </bean>
       
       
     <!-- mail -->
    <bean id="mailSender" class="org.springframework.mail.javamail.JavaMailSenderImpl">
        <property name="host" value="smtp.sungoin.com" />
        <property name="username" value="<EMAIL>" />
        <property name="password" value="BaiNao_111111" />
        <property name="defaultEncoding" value="UTF-8"></property>
        <property name="javaMailProperties">
            <props>
                <prop key="mail.smtp.auth">true</prop>
                <prop key="mail.smtp.timeout">20000</prop>
            </props>
        </property>
    </bean>

</beans>
