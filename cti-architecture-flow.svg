<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .box { fill: #ecf0f1; stroke: #34495e; stroke-width: 2; rx: 5; }
      .engine-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 2; rx: 5; }
      .server-box { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; rx: 5; }
      .db-box { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; rx: 5; }
      .client-box { fill: #f4ecf7; stroke: #9b59b6; stroke-width: 2; rx: 5; }
      .task-box { fill: #fdedec; stroke: #e74c3c; stroke-width: 2; rx: 5; }
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .event-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .control-arrow { stroke: #3498db; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #f39c12; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" class="title">CTI服务器完整架构与流程图</text>

  <!-- 1. 启动层 -->
  <rect x="50" y="60" width="200" height="80" class="box"/>
  <text x="150" y="85" text-anchor="middle" class="subtitle">启动层</text>
  <text x="150" y="105" text-anchor="middle" class="text">CTIApplication</text>
  <text x="150" y="120" text-anchor="middle" class="small-text">Spring Boot启动</text>
  <text x="150" y="135" text-anchor="middle" class="small-text">配置引擎选择</text>

  <!-- 2. CTI引擎层 -->
  <rect x="300" y="60" width="400" height="120" class="engine-box"/>
  <text x="500" y="85" text-anchor="middle" class="subtitle">CTI引擎层 (可插拔)</text>
  
  <!-- 引擎实现 -->
  <rect x="320" y="100" width="80" height="60" class="box"/>
  <text x="360" y="120" text-anchor="middle" class="text">DJKeygoe</text>
  <text x="360" y="135" text-anchor="middle" class="small-text">东进板卡</text>
  <text x="360" y="150" text-anchor="middle" class="small-text">引擎</text>

  <rect x="420" y="100" width="80" height="60" class="box"/>
  <text x="460" y="120" text-anchor="middle" class="text">ShctiEngine</text>
  <text x="460" y="135" text-anchor="middle" class="small-text">三汇CTI</text>
  <text x="460" y="150" text-anchor="middle" class="small-text">中间件</text>

  <rect x="520" y="100" width="80" height="60" class="box"/>
  <text x="560" y="120" text-anchor="middle" class="text">ShsipEngine</text>
  <text x="560" y="135" text-anchor="middle" class="small-text">SIP</text>
  <text x="560" y="150" text-anchor="middle" class="small-text">方案</text>

  <rect x="620" y="100" width="80" height="60" class="box"/>
  <text x="660" y="120" text-anchor="middle" class="text">XccEngine</text>
  <text x="660" y="135" text-anchor="middle" class="small-text">XCC+NATS</text>
  <text x="660" y="150" text-anchor="middle" class="small-text">方案</text>

  <!-- 3. 服务器层 -->
  <rect x="750" y="60" width="300" height="120" class="server-box"/>
  <text x="900" y="85" text-anchor="middle" class="subtitle">CTI服务器层</text>
  
  <rect x="770" y="100" width="120" height="60" class="box"/>
  <text x="830" y="120" text-anchor="middle" class="text">CTIServerImpl</text>
  <text x="830" y="135" text-anchor="middle" class="small-text">话务控制</text>
  <text x="830" y="150" text-anchor="middle" class="small-text">事件处理</text>

  <rect x="910" y="100" width="120" height="60" class="box"/>
  <text x="970" y="120" text-anchor="middle" class="text">MinaTimeServer</text>
  <text x="970" y="135" text-anchor="middle" class="small-text">Socket服务</text>
  <text x="970" y="150" text-anchor="middle" class="small-text">事件推送</text>

  <!-- 4. 事件处理层 -->
  <rect x="300" y="220" width="400" height="100" class="box"/>
  <text x="500" y="245" text-anchor="middle" class="subtitle">事件处理与适配层</text>
  
  <rect x="320" y="260" width="150" height="50" class="box"/>
  <text x="395" y="280" text-anchor="middle" class="text">CTIEventHandlerAdapt</text>
  <text x="395" y="295" text-anchor="middle" class="small-text">事件适配与分发</text>

  <rect x="530" y="260" width="150" height="50" class="box"/>
  <text x="605" y="280" text-anchor="middle" class="text">ProcessService</text>
  <text x="605" y="295" text-anchor="middle" class="small-text">业务处理与持久化</text>

  <!-- 5. 客户端层 -->
  <rect x="1100" y="60" width="250" height="120" class="client-box"/>
  <text x="1225" y="85" text-anchor="middle" class="subtitle">客户端层</text>
  
  <rect x="1120" y="100" width="100" height="60" class="box"/>
  <text x="1170" y="120" text-anchor="middle" class="text">坐席前端</text>
  <text x="1170" y="135" text-anchor="middle" class="small-text">Socket连接</text>
  <text x="1170" y="150" text-anchor="middle" class="small-text">事件接收</text>

  <rect x="1230" y="100" width="100" height="60" class="box"/>
  <text x="1280" y="120" text-anchor="middle" class="text">外部系统</text>
  <text x="1280" y="135" text-anchor="middle" class="small-text">控制指令</text>
  <text x="1280" y="150" text-anchor="middle" class="small-text">业务集成</text>

  <!-- 6. 数据库层 -->
  <rect x="50" y="360" width="600" height="150" class="db-box"/>
  <text x="350" y="385" text-anchor="middle" class="subtitle">数据持久化层 (MySQL)</text>
  
  <rect x="70" y="400" width="130" height="80" class="box"/>
  <text x="135" y="420" text-anchor="middle" class="text">orig_talk_note_x</text>
  <text x="135" y="435" text-anchor="middle" class="small-text">通话记录</text>
  <text x="135" y="450" text-anchor="middle" class="small-text">每日分表</text>
  <text x="135" y="465" text-anchor="middle" class="small-text">号码加密存储</text>

  <rect x="220" y="400" width="130" height="80" class="box"/>
  <text x="285" y="420" text-anchor="middle" class="text">t_base_connect_trunk</text>
  <text x="285" y="435" text-anchor="middle" class="small-text">通话状态跟踪</text>
  <text x="285" y="450" text-anchor="middle" class="small-text">通道占用管理</text>
  <text x="285" y="465" text-anchor="middle" class="small-text">超时检查</text>

  <rect x="370" y="400" width="130" height="80" class="box"/>
  <text x="435" y="420" text-anchor="middle" class="text">t_base_device_trunk</text>
  <text x="435" y="435" text-anchor="middle" class="small-text">中继使用统计</text>
  <text x="435" y="450" text-anchor="middle" class="small-text">资源监控</text>
  <text x="435" y="465" text-anchor="middle" class="small-text">告警数据</text>

  <rect x="520" y="400" width="110" height="80" class="box"/>
  <text x="575" y="420" text-anchor="middle" class="text">original_call</text>
  <text x="575" y="435" text-anchor="middle" class="small-text">来电号码</text>
  <text x="575" y="450" text-anchor="middle" class="small-text">转接映射</text>
  <text x="575" y="465" text-anchor="middle" class="small-text">查询表</text>

  <!-- 7. 定时任务层 -->
  <rect x="750" y="360" width="600" height="150" class="task-box"/>
  <text x="1050" y="385" text-anchor="middle" class="subtitle">定时任务与治理层</text>
  
  <rect x="770" y="400" width="120" height="80" class="box"/>
  <text x="830" y="420" text-anchor="middle" class="text">CommonTask</text>
  <text x="830" y="435" text-anchor="middle" class="small-text">每日2点建表</text>
  <text x="830" y="450" text-anchor="middle" class="small-text">每日4点清理</text>
  <text x="830" y="465" text-anchor="middle" class="small-text">KeyUp缓存</text>

  <rect x="910" y="400" width="120" height="80" class="box"/>
  <text x="970" y="420" text-anchor="middle" class="text">使用率检查</text>
  <text x="970" y="435" text-anchor="middle" class="small-text">周期性统计</text>
  <text x="970" y="450" text-anchor="middle" class="small-text">中继使用率</text>
  <text x="970" y="465" text-anchor="middle" class="small-text">邮件告警</text>

  <rect x="1050" y="400" width="120" height="80" class="box"/>
  <text x="1110" y="420" text-anchor="middle" class="text">超长通话检查</text>
  <text x="1110" y="435" text-anchor="middle" class="small-text">定时扫描</text>
  <text x="1110" y="450" text-anchor="middle" class="small-text">超时强制</text>
  <text x="1110" y="465" text-anchor="middle" class="small-text">挂机</text>

  <rect x="1190" y="400" width="120" height="80" class="box"/>
  <text x="1250" y="420" text-anchor="middle" class="text">REST接口</text>
  <text x="1250" y="435" text-anchor="middle" class="small-text">/cti/reset</text>
  <text x="1250" y="450" text-anchor="middle" class="small-text">ConnectTrunk</text>
  <text x="1250" y="465" text-anchor="middle" class="small-text">运维操作</text>

  <!-- 8. 底层设备 -->
  <rect x="50" y="550" width="650" height="100" class="box"/>
  <text x="375" y="575" text-anchor="middle" class="subtitle">底层设备与驱动层</text>
  
  <rect x="70" y="590" width="120" height="50" class="box"/>
  <text x="130" y="610" text-anchor="middle" class="text">东进Keygoe板卡</text>
  <text x="130" y="625" text-anchor="middle" class="small-text">硬件驱动</text>

  <rect x="210" y="590" width="120" height="50" class="box"/>
  <text x="270" y="610" text-anchor="middle" class="text">三汇CTI设备</text>
  <text x="270" y="625" text-anchor="middle" class="small-text">Socket通信</text>

  <rect x="350" y="590" width="120" height="50" class="box"/>
  <text x="410" y="610" text-anchor="middle" class="text">SIP服务器</text>
  <text x="410" y="625" text-anchor="middle" class="small-text">SIP协议</text>

  <rect x="490" y="590" width="120" height="50" class="box"/>
  <text x="550" y="610" text-anchor="middle" class="text">XCC+NATS</text>
  <text x="550" y="625" text-anchor="middle" class="small-text">消息队列</text>

  <rect x="630" y="590" width="120" height="50" class="box"/>
  <text x="690" y="610" text-anchor="middle" class="text">PSTN/PBX</text>
  <text x="690" y="625" text-anchor="middle" class="small-text">电话网络</text>

  <!-- 连接线 -->
  <!-- 启动到引擎 -->
  <line x1="250" y1="100" x2="300" y2="100" class="arrow"/>
  
  <!-- 引擎到服务器 -->
  <line x1="700" y1="120" x2="750" y2="120" class="arrow"/>
  
  <!-- 服务器到客户端 -->
  <line x1="1050" y1="120" x2="1100" y2="120" class="arrow"/>
  
  <!-- 引擎到事件处理 -->
  <line x1="500" y1="180" x2="500" y2="220" class="event-arrow"/>
  
  <!-- 事件处理到数据库 -->
  <line x1="500" y1="320" x2="350" y2="360" class="data-arrow"/>
  
  <!-- 服务器到定时任务 -->
  <line x1="900" y1="180" x2="1050" y2="360" class="arrow"/>
  
  <!-- 底层设备到引擎 -->
  <line x1="375" y1="550" x2="500" y2="180" class="event-arrow"/>

  <!-- 事件流程说明 -->
  <rect x="750" y="550" width="600" height="400" class="box"/>
  <text x="1050" y="575" text-anchor="middle" class="subtitle">关键事件流程说明</text>
  
  <!-- 上行事件流程 -->
  <text x="770" y="600" class="text">1. 上行事件流程 (设备→客户端):</text>
  <text x="790" y="620" class="small-text">① 底层设备产生事件 (来电/振铃/摘机/挂机/DTMF等)</text>
  <text x="790" y="635" class="small-text">② CTIEngine.processEvent() 接收并分类处理</text>
  <text x="790" y="650" class="small-text">③ CTIEventHandlerAdapt 适配事件格式</text>
  <text x="790" y="665" class="small-text">④ 构造 CTIRemoteReq JSON消息</text>
  <text x="790" y="680" class="small-text">⑤ MinaTimeServer 广播到所有连接的客户端</text>
  <text x="790" y="695" class="small-text">⑥ ProcessService 异步落库 (通话记录/状态跟踪)</text>

  <!-- 下行控制流程 -->
  <text x="770" y="720" class="text">2. 下行控制流程 (客户端→设备):</text>
  <text x="790" y="740" class="small-text">① 客户端通过Socket发送控制指令</text>
  <text x="790" y="755" class="small-text">② MINA ServerHandler 解析指令</text>
  <text x="790" y="770" class="small-text">③ CTIServerImpl 调用对应控制方法</text>
  <text x="790" y="785" class="small-text">④ CTIEngine 执行具体操作 (应答/播放/录音/挂机等)</text>
  <text x="790" y="800" class="small-text">⑤ 底层设备执行后产生状态事件，回到上行流程</text>

  <!-- 数据治理流程 -->
  <text x="770" y="825" class="text">3. 数据治理与监控:</text>
  <text x="790" y="845" class="small-text">① 每日2点自动创建次日分表</text>
  <text x="790" y="860" class="small-text">② 周期性检查中继使用率，超阈值邮件告警</text>
  <text x="790" y="875" class="small-text">③ 定时扫描超长通话，强制挂机释放资源</text>
  <text x="790" y="890" class="small-text">④ 清理无效的DTMF按键缓存</text>
  <text x="790" y="905" class="small-text">⑤ REST接口支持运维操作 (重置通道状态等)</text>

  <!-- 关键特性 -->
  <text x="770" y="930" class="text">4. 关键技术特性:</text>
  <text x="790" y="950" class="small-text">• 多引擎可插拔架构，配置切换不同厂商设备</text>
  <text x="790" y="965" class="small-text">• 统一事件模型，屏蔽底层差异</text>
  <text x="790" y="980" class="small-text">• 异步事件处理，高并发支持</text>
  <text x="790" y="995" class="small-text">• 数据加密存储，按日分表，自动化运维</text>

</svg>
