<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; fill: #2c3e50; }
      .subtitle { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; fill: #34495e; }
      .text { font-family: Arial, sans-serif; font-size: 12px; fill: #2c3e50; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; fill: #7f8c8d; }
      .method-text { font-family: Arial, sans-serif; font-size: 11px; fill: #e74c3c; font-weight: bold; }
      .class-text { font-family: Arial, sans-serif; font-size: 11px; fill: #3498db; font-weight: bold; }
      
      .start-box { fill: #e8f5e8; stroke: #27ae60; stroke-width: 3; rx: 8; }
      .process-box { fill: #e8f4fd; stroke: #3498db; stroke-width: 2; rx: 5; }
      .decision-box { fill: #fdf2e9; stroke: #e67e22; stroke-width: 2; rx: 5; }
      .handler-box { fill: #f4ecf7; stroke: #9b59b6; stroke-width: 2; rx: 5; }
      .output-box { fill: #fdedec; stroke: #e74c3c; stroke-width: 2; rx: 5; }
      .db-box { fill: #fff5f5; stroke: #c0392b; stroke-width: 2; rx: 5; }
      
      .arrow { stroke: #2c3e50; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .event-arrow { stroke: #e74c3c; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .data-arrow { stroke: #f39c12; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .decision-arrow { stroke: #e67e22; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#2c3e50" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="800" y="30" text-anchor="middle" class="title">handlerEvent 事件处理完整流程图</text>

  <!-- 1. 事件触发源 -->
  <rect x="50" y="60" width="200" height="80" class="start-box"/>
  <text x="150" y="85" text-anchor="middle" class="subtitle">事件触发源</text>
  <text x="150" y="105" text-anchor="middle" class="text">底层设备事件</text>
  <text x="150" y="120" text-anchor="middle" class="small-text">来电/振铃/摘机/挂机</text>
  <text x="150" y="135" text-anchor="middle" class="small-text">外呼/DTMF/录音等</text>

  <!-- 2. EsrFuncImpl 事件回调 -->
  <rect x="300" y="60" width="250" height="80" class="process-box"/>
  <text x="425" y="85" text-anchor="middle" class="subtitle">EsrFuncImpl</text>
  <text x="425" y="105" text-anchor="middle" class="class-text">EsrFuncImpl.EsrCallBack()</text>
  <text x="425" y="120" text-anchor="middle" class="method-text">GlobalVar.handlerEvent()</text>
  <text x="425" y="135" text-anchor="middle" class="small-text">设备事件回调入口</text>

  <!-- 3. Global.handlerEvent 核心处理 -->
  <rect x="600" y="60" width="300" height="120" class="process-box"/>
  <text x="750" y="85" text-anchor="middle" class="subtitle">Global.handlerEvent()</text>
  <text x="750" y="105" text-anchor="middle" class="class-text">com.sungoin.cti.engine.impl.djkeygoe.local.Global</text>
  <text x="750" y="120" text-anchor="middle" class="method-text">handlerEvent(DeviceID_t, Acs_Evt_t, Object)</text>
  <text x="750" y="135" text-anchor="middle" class="small-text">① 解析设备类型 (TRUNK/VOC/CONF)</text>
  <text x="750" y="150" text-anchor="middle" class="small-text">② 构造 EventData</text>
  <text x="750" y="165" text-anchor="middle" class="small-text">③ 根据事件类型分派处理</text>

  <!-- 4. 设备类型判断 -->
  <rect x="950" y="60" width="200" height="120" class="decision-box"/>
  <text x="1050" y="85" text-anchor="middle" class="subtitle">设备类型判断</text>
  <text x="1050" y="105" text-anchor="middle" class="text">dev.m_s16DeviceMain</text>
  <text x="1050" y="125" text-anchor="middle" class="small-text">INTERFACE_CH → TRUNK</text>
  <text x="1050" y="140" text-anchor="middle" class="small-text">VOICE → VOC</text>
  <text x="1050" y="155" text-anchor="middle" class="small-text">CONFERENCE → CONF</text>
  <text x="1050" y="170" text-anchor="middle" class="small-text">设置 EventSource</text>

  <!-- 5. 事件类型处理分支 -->
  <rect x="50" y="220" width="1500" height="150" class="decision-box"/>
  <text x="800" y="245" text-anchor="middle" class="subtitle">事件类型处理 (switch nEvtType)</text>
  
  <!-- 各种事件类型 -->
  <rect x="70" y="270" width="180" height="80" class="process-box"/>
  <text x="160" y="290" text-anchor="middle" class="text">XMS_EVT_CALLIN</text>
  <text x="160" y="305" text-anchor="middle" class="method-text">EventType.CALLIN</text>
  <text x="160" y="320" text-anchor="middle" class="small-text">来电事件处理</text>
  <text x="160" y="335" text-anchor="middle" class="small-text">设置caller/callee</text>

  <rect x="270" y="270" width="180" height="80" class="process-box"/>
  <text x="360" y="290" text-anchor="middle" class="text">XMS_EVT_ALERTCALL</text>
  <text x="360" y="305" text-anchor="middle" class="method-text">EventType.ALERTCALL</text>
  <text x="360" y="320" text-anchor="middle" class="small-text">振铃事件处理</text>
  <text x="360" y="335" text-anchor="middle" class="small-text">设置状态和错误码</text>

  <rect x="470" y="270" width="180" height="80" class="process-box"/>
  <text x="560" y="290" text-anchor="middle" class="text">XMS_EVT_ANSWERCALL</text>
  <text x="560" y="305" text-anchor="middle" class="method-text">EventType.ANSWERCALL</text>
  <text x="560" y="320" text-anchor="middle" class="small-text">摘机事件处理</text>
  <text x="560" y="335" text-anchor="middle" class="small-text">Change_State(TRK_USED)</text>

  <rect x="670" y="270" width="180" height="80" class="process-box"/>
  <text x="760" y="290" text-anchor="middle" class="text">XMS_EVT_CLEARCALL</text>
  <text x="760" y="305" text-anchor="middle" class="method-text">EventType.CLEARCALL</text>
  <text x="760" y="320" text-anchor="middle" class="small-text">挂机事件处理</text>
  <text x="760" y="335" text-anchor="middle" class="small-text">ResetTrunk()</text>

  <rect x="870" y="270" width="180" height="80" class="process-box"/>
  <text x="960" y="290" text-anchor="middle" class="text">XMS_EVT_CALLOUT</text>
  <text x="960" y="305" text-anchor="middle" class="method-text">EventType.CALLOUT</text>
  <text x="960" y="320" text-anchor="middle" class="small-text">外呼结果处理</text>
  <text x="960" y="335" text-anchor="middle" class="small-text">清除CALL_OUT_MAP</text>

  <rect x="1070" y="270" width="180" height="80" class="process-box"/>
  <text x="1160" y="290" text-anchor="middle" class="text">XMS_EVT_RECEIVEDTMF</text>
  <text x="1160" y="305" text-anchor="middle" class="method-text">EventType.RECEIVEDTMF</text>
  <text x="1160" y="320" text-anchor="middle" class="small-text">DTMF按键处理</text>
  <text x="1160" y="335" text-anchor="middle" class="small-text">设置dtmf字符</text>

  <rect x="1270" y="270" width="180" height="80" class="process-box"/>
  <text x="1360" y="290" text-anchor="middle" class="text">其他事件类型</text>
  <text x="1360" y="305" text-anchor="middle" class="method-text">PLAY/RECORD/CONF</text>
  <text x="1360" y="320" text-anchor="middle" class="small-text">播放/录音/会议</text>
  <text x="1360" y="335" text-anchor="middle" class="small-text">事件处理</text>

  <!-- 6. 创建 DJKeygoeEvent -->
  <rect x="600" y="400" width="300" height="80" class="process-box"/>
  <text x="750" y="425" text-anchor="middle" class="subtitle">创建统一事件对象</text>
  <text x="750" y="445" text-anchor="middle" class="class-text">new DJKeygoeEvent(source, type, event)</text>
  <text x="750" y="460" text-anchor="middle" class="method-text">engine.processEvent(djEvent)</text>
  <text x="750" y="475" text-anchor="middle" class="small-text">转发到引擎处理</text>

  <!-- 7. DJKeygoeEngine.processEvent -->
  <rect x="950" y="400" width="300" height="80" class="handler-box"/>
  <text x="1100" y="425" text-anchor="middle" class="subtitle">DJKeygoeEngine</text>
  <text x="1100" y="445" text-anchor="middle" class="class-text">DJKeygoeEngine.processEvent()</text>
  <text x="1100" y="460" text-anchor="middle" class="method-text">根据EventSource/EventType分派</text>
  <text x="1100" y="475" text-anchor="middle" class="small-text">调用对应的eventHandler方法</text>

  <!-- 8. CTIEventHandlerAdapt 处理 -->
  <rect x="300" y="520" width="350" height="100" class="handler-box"/>
  <text x="475" y="545" text-anchor="middle" class="subtitle">CTIEventHandlerAdapt</text>
  <text x="475" y="565" text-anchor="middle" class="class-text">CTIEventHandlerAdapt.callIncome()</text>
  <text x="475" y="580" text-anchor="middle" class="class-text">CTIEventHandlerAdapt.alert()</text>
  <text x="475" y="595" text-anchor="middle" class="class-text">CTIEventHandlerAdapt.offHook()</text>
  <text x="475" y="610" text-anchor="middle" class="small-text">等各种事件处理方法</text>

  <!-- 9. 构造 CTIRemoteReq -->
  <rect x="700" y="520" width="300" height="100" class="output-box"/>
  <text x="850" y="545" text-anchor="middle" class="subtitle">构造推送消息</text>
  <text x="850" y="565" text-anchor="middle" class="class-text">CTIRemoteReq.getEvent()</text>
  <text x="850" y="580" text-anchor="middle" class="method-text">putParamField(key, value)</text>
  <text x="850" y="595" text-anchor="middle" class="method-text">req.send()</text>
  <text x="850" y="610" text-anchor="middle" class="small-text">构造JSON消息并推送</text>

  <!-- 10. MinaTimeServer 广播 -->
  <rect x="1050" y="520" width="300" height="100" class="output-box"/>
  <text x="1200" y="545" text-anchor="middle" class="subtitle">MinaTimeServer</text>
  <text x="1200" y="565" text-anchor="middle" class="class-text">MinaTimeServer.broadcastMessage()</text>
  <text x="1200" y="580" text-anchor="middle" class="method-text">session.write(message)</text>
  <text x="1200" y="595" text-anchor="middle" class="small-text">向所有连接的客户端</text>
  <text x="1200" y="610" text-anchor="middle" class="small-text">广播JSON事件消息</text>

  <!-- 11. 数据持久化 -->
  <rect x="50" y="660" width="350" height="100" class="db-box"/>
  <text x="225" y="685" text-anchor="middle" class="subtitle">数据持久化</text>
  <text x="225" y="705" text-anchor="middle" class="class-text">CTIServerImpl.saveOrigTalkNote()</text>
  <text x="225" y="720" text-anchor="middle" class="class-text">ProcessService.saveCallData()</text>
  <text x="225" y="735" text-anchor="middle" class="method-text">addConnectTrunk() / removeConnectTrunk()</text>
  <text x="225" y="750" text-anchor="middle" class="small-text">异步保存到MySQL数据库</text>

  <!-- 12. 客户端接收 -->
  <rect x="1400" y="520" width="150" height="100" class="output-box"/>
  <text x="1475" y="545" text-anchor="middle" class="subtitle">客户端</text>
  <text x="1475" y="565" text-anchor="middle" class="text">坐席前端</text>
  <text x="1475" y="580" text-anchor="middle" class="text">外部系统</text>
  <text x="1475" y="595" text-anchor="middle" class="small-text">接收JSON</text>
  <text x="1475" y="610" text-anchor="middle" class="small-text">事件消息</text>

  <!-- 连接线 -->
  <!-- 事件触发到回调 -->
  <line x1="250" y1="100" x2="300" y2="100" class="event-arrow"/>
  
  <!-- 回调到handlerEvent -->
  <line x1="550" y1="100" x2="600" y2="100" class="event-arrow"/>
  
  <!-- handlerEvent到设备判断 -->
  <line x1="900" y1="120" x2="950" y2="120" class="decision-arrow"/>
  
  <!-- 设备判断到事件处理 -->
  <line x1="1050" y1="180" x2="800" y2="220" class="decision-arrow"/>
  
  <!-- 事件处理到创建事件对象 -->
  <line x1="800" y1="370" x2="750" y2="400" class="event-arrow"/>
  
  <!-- 创建事件对象到引擎处理 -->
  <line x1="900" y1="440" x2="950" y2="440" class="event-arrow"/>
  
  <!-- 引擎处理到事件适配器 -->
  <line x1="1100" y1="480" x2="475" y2="520" class="event-arrow"/>
  
  <!-- 事件适配器到消息构造 -->
  <line x1="650" y1="570" x2="700" y2="570" class="arrow"/>
  
  <!-- 消息构造到广播 -->
  <line x1="1000" y1="570" x2="1050" y2="570" class="arrow"/>
  
  <!-- 广播到客户端 -->
  <line x1="1350" y1="570" x2="1400" y2="570" class="arrow"/>
  
  <!-- 事件适配器到数据持久化 -->
  <line x1="475" y1="620" x2="225" y2="660" class="data-arrow"/>

  <!-- 详细说明区域 -->
  <rect x="50" y="800" width="1500" height="350" class="process-box"/>
  <text x="800" y="825" text-anchor="middle" class="subtitle">handlerEvent 方法详细处理流程说明</text>
  
  <!-- 上游处理 -->
  <text x="70" y="850" class="text">1. 上游事件触发链路:</text>
  <text x="90" y="870" class="small-text">① 底层设备产生事件 → 东进驱动回调</text>
  <text x="90" y="885" class="small-text">② EsrFuncImpl.EsrCallBack() 接收驱动回调</text>
  <text x="90" y="900" class="small-text">③ GlobalVar.DispEventInfo() 显示事件信息</text>
  <text x="90" y="915" class="small-text">④ GlobalVar.handlerEvent() 核心事件处理入口</text>

  <!-- handlerEvent 内部处理 -->
  <text x="70" y="940" class="text">2. handlerEvent 内部处理逻辑:</text>
  <text x="90" y="960" class="small-text">① 根据 dev.m_s16DeviceMain 判断设备类型:</text>
  <text x="110" y="975" class="small-text">• XMS_DEVMAIN_INTERFACE_CH → TRUNK (中继通道)</text>
  <text x="110" y="990" class="small-text">• XMS_DEVMAIN_VOICE → VOC (语音通道)</text>
  <text x="110" y="1005" class="small-text">• XMS_DEVMAIN_CONFERENCE → CONF (会议通道)</text>
  <text x="90" y="1020" class="small-text">② 获取对应的设备结构体 (TRUNK_STRUCT/VOICE_STRUCT/CONF_STRUCT)</text>
  <text x="90" y="1035" class="small-text">③ 构造 EventData 对象，设置 deviceId、lsh、usedDeviceId</text>
  <text x="90" y="1050" class="small-text">④ 根据 acsEvtData.m_s32EventType 进行事件类型分派处理</text>

  <!-- 事件类型处理 -->
  <text x="70" y="1075" class="text">3. 主要事件类型处理:</text>
  <text x="90" y="1095" class="small-text">• XMS_EVT_CALLIN: 来电事件，设置caller/callee，更新通道状态为TRK_WAIT_ANSWERCALL</text>
  <text x="90" y="1110" class="small-text">• XMS_EVT_ANSWERCALL: 摘机事件，Change_State(OneTrk, TRK_USED)，记录摘机时间</text>
  <text x="90" y="1125" class="small-text">• XMS_EVT_CLEARCALL: 挂机事件，ResetTrunk(OneTrk) 重置通道状态</text>
  <text x="90" y="1140" class="small-text">• XMS_EVT_CALLOUT: 外呼结果，清除CALL_OUT_MAP缓存，处理成功/失败状态</text>

  <!-- 下游处理 -->
  <text x="800" y="850" class="text">4. 下游事件分发链路:</text>
  <text x="820" y="870" class="small-text">① 创建 DJKeygoeEvent(source, type, event)</text>
  <text x="820" y="885" class="small-text">② engine.processEvent() 转发到引擎</text>
  <text x="820" y="900" class="small-text">③ DJKeygoeEngine.processEvent() 根据EventSource/EventType分派</text>
  <text x="820" y="915" class="small-text">④ 调用 eventHandler 对应方法 (callIncome/alert/offHook等)</text>

  <!-- 事件适配器处理 -->
  <text x="800" y="940" class="text">5. CTIEventHandlerAdapt 处理:</text>
  <text x="820" y="960" class="small-text">① 提取 EventData 中的关键信息 (caller/callee/deviceID/lsh等)</text>
  <text x="820" y="975" class="small-text">② 构造 CTIRemoteReq JSON消息对象</text>
  <text x="820" y="990" class="small-text">③ putParamField() 添加业务参数</text>
  <text x="820" y="1005" class="small-text">④ req.send() 通过MinaTimeServer广播到所有客户端</text>
  <text x="820" y="1020" class="small-text">⑤ 异步调用 ProcessService 进行数据持久化</text>

  <!-- 关键类和方法 -->
  <text x="800" y="1045" class="text">6. 涉及的关键类和方法:</text>
  <text x="820" y="1065" class="small-text">• Global.handlerEvent() - 核心事件处理入口</text>
  <text x="820" y="1080" class="small-text">• DJKeygoeEngine.processEvent() - 引擎事件分派</text>
  <text x="820" y="1095" class="small-text">• CTIEventHandlerAdapt.* - 各种事件处理方法</text>
  <text x="820" y="1110" class="small-text">• CTIRemoteReq.send() - 消息推送</text>
  <text x="820" y="1125" class="small-text">• MinaTimeServer.broadcastMessage() - Socket广播</text>
  <text x="820" y="1140" class="small-text">• ProcessService.saveCallData() - 数据持久化</text>

</svg>
